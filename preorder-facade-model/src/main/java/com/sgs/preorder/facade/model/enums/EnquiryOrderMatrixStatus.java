package com.sgs.preorder.facade.model.enums;

public enum EnquiryOrderMatrixStatus {

    New("0","New"),
    Completed("1","Completed");

    private String code;
    private String message;

    EnquiryOrderMatrixStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
