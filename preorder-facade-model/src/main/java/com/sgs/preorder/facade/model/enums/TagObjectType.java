package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum TagObjectType {
    Order(1, "Order"),
    Enquiry(2, "Enquiry"),
    Quotation(3,"Quotation");

    private Integer status;
    private String value;

    TagObjectType(Integer status, String value) {
        this.status = status;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, TagObjectType> maps = new HashMap<Integer, TagObjectType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (TagObjectType enu : TagObjectType.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static boolean check(Integer status, TagObjectType execType) {
        if (status == null || status.intValue() <= 0 || !maps.containsKey(status.intValue())) {
            return false;
        }
        return maps.get(status) == execType;
    }

    public static String getValue(Integer status) {
        if (status == null || status == 0) {
            return null;
        }
        TagObjectType tagObjectStatus = maps.get(status);
        return tagObjectStatus == null ? null : tagObjectStatus.getValue();
    }
}
