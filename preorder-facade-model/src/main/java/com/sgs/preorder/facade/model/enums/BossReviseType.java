package com.sgs.preorder.facade.model.enums;

public enum BossReviseType {
    Extra(1, "Extra"),
    Replace(2, "Replace");

    private Integer status;
    private String value;

    BossReviseType(Integer status, String value) {
        this.status = status;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public Integer getStatus() {
        return status;
    }

    public static String getReviseValueByStatus(Integer status){
        for (BossReviseType bossReviseType : BossReviseType.values()) {
            //jira-11232
            if(bossReviseType.getStatus().equals(status)){
                return bossReviseType.getValue();
            }
        }
        return "";
    }
}
