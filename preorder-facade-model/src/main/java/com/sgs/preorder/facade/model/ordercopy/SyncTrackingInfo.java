package com.sgs.preorder.facade.model.ordercopy;

import com.alibaba.fastjson.annotation.JSONField;
import com.sgs.framework.core.base.BaseProductLine;

public class SyncTrackingInfo extends BaseProductLine {
    /**
     *
     */
    private String actionType;
    /**
     *
     */
    /*@JsonProperty("BU")*/
    @JSONField(name = "BU")
    private String bu;
    /**
     *
     */
    private String instanceId;
    /**
     * order
     */
    private String instanceTypeCode;
    /**
     * labInstancePO.getLabCode()
     */
    private String lab;
    /**
     *
     */
    private String operator;
    /**
     *
     */
    private String parentInstanceId;
    /**
     *
     */
    private String parentInstanceTypeCode;
    /**
     * point
     */
    private String remark;
    /**
     * point
     */
    private String pointCode;
    /**
     * sgsToken
     */
    private String sgsToken;
    /**
     * point
     */
    private String status;
    /**
     * 10
     */
    private Integer system;
    /**
     * slOrder.getResponsibleTeamCode()
     */
    private String team;
    /**
     * slOrder.getResponsibleTeamCode()
     */
    private String responsibleTeamCode;
    /**
     * slOrder.getcSName()
     */
    private String csName;

    @JSONField(name = "isToken")
    private boolean isToken;

    private Long pointActionDate;

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getInstanceTypeCode() {
        return instanceTypeCode;
    }

    public void setInstanceTypeCode(String instanceTypeCode) {
        this.instanceTypeCode = instanceTypeCode;
    }

    public String getLab() {
        return lab;
    }

    public void setLab(String lab) {
        this.lab = lab;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getParentInstanceId() {
        return parentInstanceId;
    }

    public void setParentInstanceId(String parentInstanceId) {
        this.parentInstanceId = parentInstanceId;
    }

    public String getParentInstanceTypeCode() {
        return parentInstanceTypeCode;
    }

    public void setParentInstanceTypeCode(String parentInstanceTypeCode) {
        this.parentInstanceTypeCode = parentInstanceTypeCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPointCode() {
        return pointCode;
    }

    public void setPointCode(String pointCode) {
        this.pointCode = pointCode;
    }

    public String getSgsToken() {
        return sgsToken;
    }

    public void setSgsToken(String sgsToken) {
        this.sgsToken = sgsToken;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getSystem() {
        return system;
    }

    public void setSystem(Integer system) {
        this.system = system;
    }

    public String getTeam() {
        return team;
    }

    public void setTeam(String team) {
        this.team = team;
    }

    public String getResponsibleTeamCode() {
        return responsibleTeamCode;
    }

    public void setResponsibleTeamCode(String responsibleTeamCode) {
        this.responsibleTeamCode = responsibleTeamCode;
    }

    public String getCsName() {
        return csName;
    }

    public void setCsName(String csName) {
        this.csName = csName;
    }

    public boolean isToken() {
        return isToken;
    }

    public void setToken(boolean token) {
        isToken = token;
    }

    public Long getPointActionDate() {
        return pointActionDate;
    }

    public void setPointActionDate(Long pointActionDate) {
        this.pointActionDate = pointActionDate;
    }
}
