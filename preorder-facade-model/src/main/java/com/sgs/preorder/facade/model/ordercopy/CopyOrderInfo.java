package com.sgs.preorder.facade.model.ordercopy;

import com.sgs.framework.core.base.BaseProductLine;

import java.util.List;

public class CopyOrderInfo extends BaseProductLine {
    /**
     *
     */
    private String orderId;
    /**
     *
     */
    private String orderNo;
    /**
     *
     */
    private List<NoCopyTestLineInfo> tlRes;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<NoCopyTestLineInfo> getTlRes() {
        return tlRes;
    }

    public void setTlRes(List<NoCopyTestLineInfo> tlRes) {
        this.tlRes = tlRes;
    }
}
