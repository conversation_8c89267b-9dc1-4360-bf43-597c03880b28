package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum PrimaryTypeEnums {
    ATTACHMENT("attachment","attachment");

    private final String code;
    private final String message;

    PrimaryTypeEnums(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<String, PrimaryTypeEnums> maps = new HashMap<String, PrimaryTypeEnums>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (PrimaryTypeEnums type : PrimaryTypeEnums.values()) {
                put(type.getCode().toLowerCase(), type);
            }
        }
    };

    public static PrimaryTypeEnums getCode(String code) {
        if (code == null || !maps.containsKey(code.toLowerCase())) {
            return null;
        }
        return maps.get(code.toLowerCase());
    }

    public static boolean check(String code, PrimaryTypeEnums objectType) {
        if (code == null || objectType == null || !maps.containsKey(code.toLowerCase())){
            return false;
        }
        return maps.get(code.toLowerCase()) == objectType;
    }
}
