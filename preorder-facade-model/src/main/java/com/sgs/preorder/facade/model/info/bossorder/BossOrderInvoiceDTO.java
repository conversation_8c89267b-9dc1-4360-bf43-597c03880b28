package com.sgs.preorder.facade.model.info.bossorder;


import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 导出查询报价单明细
 */
@Getter
@Setter
public class BossOrderInvoiceDTO {

    private String invoiceNo;
    private String invoiceDate;
    private String applicantCustomerCode;
    private String applicantNameEN;
    private String applicantNameCN;
    private String applicantContactName;
    private String payerCustomerCode;
    private String payerNameEN;
    private String payerNameCN;
    private String payerContactName;
    private String buyerCustomerCode;
    private String buyerNameEN;
    private String agentCode;
    private String agentName;
    private String bossOrderNo;
    private String orderNo;
    private String reportNos;
    private String reportApproveDate;
    private String tax;
    private BigDecimal quantity;
    private BigDecimal unitPrice;
    private String lineCurrency;
    private BigDecimal discount;
    private BigDecimal exchangeRate;
    private BigDecimal surCharge;
    private BigDecimal nAmt;
    private String currencyCode;
    private BigDecimal amount;
    private String serviceType;
    private String itemNo;
    private String serviceItemName;
    private String createDate;
    private String qtName;
    private String csName;
    private String responsibleTeam;
    private String caseType;
    private String costcenter;
    private String projectTemplate;
    private String refNo;
    private String countryOfDestination;
    private String fCode;
    private String location;
    private String productName;


}
