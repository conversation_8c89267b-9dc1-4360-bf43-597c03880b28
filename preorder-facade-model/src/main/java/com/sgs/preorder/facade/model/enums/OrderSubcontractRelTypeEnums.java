package com.sgs.preorder.facade.model.enums;

public enum OrderSubcontractRelTypeEnums {

	DFF_cover_page_template(1,"DFF cover page template"),
	DFF_Grid_template(2,"cover page template"),
	Care_Label(3,"Care Label"),
	Attachment(4,"Attachment");

	private Integer type;
	private String typeName;

	OrderSubcontractRelTypeEnums(Integer type, String typeName){
		this.type = type;
		this.typeName = typeName;
	}

	public Integer getType() {
		return type;
	}

	public String getTypeName() {
		return typeName;
	}
}
