package com.sgs.preorder.facade.model.common;

import java.math.BigDecimal;
import java.util.*;

public class StringUtil {
    /**
     *
     * @param intValue
     * @return
     */
    public static int hashCode(final Integer intValue) {
        if (intValue == null){
            return 0;
        }
        return intValue.intValue();
    }

    /**
     *
     * @param strValue
     * @return
     */
    public static int hashCode(final String strValue) {
        if (strValue == null || strValue.length() <= 0){
            return 0;
        }
        return strValue.hashCode();
    }

    /**
     *
     * @param longValue
     * @return
     */
    public static int hashCode(final Long longValue) {
        if (longValue == null){
            return 0;
        }
        return longValue.hashCode();
    }

    /**
     *
     * @param bigValue
     * @return
     */
    public static int hashCode(final BigDecimal bigValue) {
        if (bigValue == null || bigValue.floatValue() <= 0){
            return 0;
        }
        return bigValue.hashCode();
    }

    /**
     *
     * @param byteValue
     * @return
     */
    public static int hashCode(final Byte byteValue) {
        if (byteValue == null){
            return 0;
        }
        return byteValue.hashCode();
    }

    /**
     *
     * @param dateValue
     * @return
     */
    public static int hashCode(final Date dateValue) {
        if (dateValue == null){
            return 0;
        }
        return dateValue.hashCode();
    }

    /**
     *
     * @param lists
     * @return
     */
    public static int hashCode(final List<String> lists) {
        if (lists == null || lists.isEmpty()){
            return 0;
        }
        List<String> newLists = new ArrayList<>();
        for (String valNo: lists){
            if (valNo == null || valNo.length() <= 0){
                continue;
            }
            newLists.add(valNo);
        }
        newLists.sort(Comparator.comparing(String::toString));
        StringBuilder append = new StringBuilder();
        for (String valNo: newLists){
            if (valNo == null || valNo.length() <= 0){
                continue;
            }
            append.append(valNo).append(";");
        }
        return append.toString().hashCode();
    }

    /**
     *
     * @param lists
     * @return
     */
    public static int strToHash(final String lists) {
        if (lists == null || lists.length() <= 0){
            return 0;
        }
        List<String> newLists = Arrays.asList(lists.split(";"));
        newLists.sort(Comparator.comparing(String::toString));
        StringBuilder append = new StringBuilder();
        for (String valNo: newLists){
            if (valNo == null || valNo.length() <= 0){
                continue;
            }
            append.append(valNo).append(";");
        }
        return append.toString().hashCode();
    }

    /**
     *
     * @param strVal
     * @param splitVal
     * @return
     */
    public static String getSplitVal(String strVal, String splitVal){
        if (strVal == null || splitVal == null || strVal.length() <= 0){
            return strVal;
        }
        int lastIndex = strVal.lastIndexOf(splitVal);
        if (lastIndex < 0){
            return strVal;
        }
        return strVal.substring(lastIndex+1);
    }
}
