package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum CustomerWeightType {
    None(0, "None"),
    TestPackage(1, "TestPackage"),
    ServiceRequirement(2, "ServiceRequirement"),
    CAL(3, "CAL"),
    TrfTemplate(4, "TrfTemplate"),
    TlPrice(5, "TlPrice")
    ;

    private final int status;
    private final String code;

    CustomerWeightType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return code;
    }

    public static final Map<String, CustomerWeightType> maps = new HashMap<String, CustomerWeightType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (CustomerWeightType customerType : CustomerWeightType.values()) {
                put(customerType.getCode().toUpperCase(), customerType);
            }
        }
    };

    public static CustomerWeightType getCode(String code) {
        if (code == null || code.length() == 0 || !maps.containsKey(code.toUpperCase())) {
            return CustomerWeightType.None;
        }
        return maps.get(code.toUpperCase());
    }
}
