package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum OrderCategory {
	SplitOrder(3, "SplitOrder"),
	SubSplitOrder(4, "SubSplitOrder"),
	Copied(5, "Copied"),
	;
	private Integer status;
	private String value;

	OrderCategory(Integer status, String value) {
		this.status = status;
		this.value = value;
	}

	public String getValue() {
		return value;
	}

	public Integer getStatus() {
		return status;
	}

	public static final Map<Integer, OrderCategory> maps = new HashMap<Integer, OrderCategory>() {
		private static final long serialVersionUID = -8986866330615001847L;
		{
			for (OrderCategory enu : OrderCategory.values()) {
				put(enu.getStatus(), enu);
			}
		}
	};

	public static boolean check(Integer status, OrderCategory orderCategory) {
		if (status == null || status.intValue() <= 0 || !maps.containsKey(status.intValue())) {
			return false;
		}
		return maps.get(status) == orderCategory;
	}

	public static String getValue(Integer status) {
		if (status == null || status == 0) {
			return null;
		}
		OrderCategory orderCategory = maps.get(status);
		return orderCategory == null ? null : orderCategory.getValue();
	}
}
