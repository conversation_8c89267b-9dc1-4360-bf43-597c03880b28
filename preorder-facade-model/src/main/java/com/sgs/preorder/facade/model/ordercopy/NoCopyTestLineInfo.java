package com.sgs.preorder.facade.model.ordercopy;

import com.sgs.framework.core.base.BaseProductLine;

public class NoCopyTestLineInfo extends BaseProductLine {
    /**
     *
     */
    private Integer testLineId;
    /**
     *
     */
    private String type;
    /**
     *
     */
    private String reason;

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
