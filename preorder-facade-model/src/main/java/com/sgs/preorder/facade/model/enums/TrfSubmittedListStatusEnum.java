package com.sgs.preorder.facade.model.enums;

/**
 * TRF todo list状态
 * <AUTHOR>
 * @date 2019/11/7 15:15
 */
public enum TrfSubmittedListStatusEnum {
    PENDING(0,"待处理"),
    COMPLETE(1,"处理完成"),
    IGNORE(2,"不予处理")
    ;


    TrfSubmittedListStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private String name;
    private Integer code;

    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }
}
