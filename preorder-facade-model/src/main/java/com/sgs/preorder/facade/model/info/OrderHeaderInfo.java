package com.sgs.preorder.facade.model.info;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.preorder.facade.model.annotation.ObjectSetting;
import com.sgs.preorder.facade.model.common.StringUtil;
import com.sgs.preorder.facade.model.dto.tag.TagValueSelectDTO;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

public class OrderHeaderInfo extends BaseProductLine {

    private int subcontractOrderFlag;

    /**
     *
     */
    private String newOrderId;
    // region 【tb_general_order】
    /**
     *
     */
    private String orderId;
    /**
     * Order Number
     */
    private String orderNo;

    private String externalOrderNo;
    /**
     *
     */
    private int orderStatus;
    /**
     *
     */
    private String orderStatusText;
    /**
     *
     */
    private int orderType;
    /**
     *
     */
    @ObjectSetting(code = "serviceLevel")
    private Integer serviceLevel;
    /**
     *
     */
    private int buId;
    /**
     *
     */
    private String buCode;
    /**
     *
     */
    private Integer labId;
    /**
     * orderLabInfo->Lab
     */
    private String labCode;
    /**
     *
     */
    private String labName;
    /**
     *
     */
    private int locationId;
    /**
     *
     */
    private String locationCode;
    /**
     * TODO Auto Update Due Date
     */
    @ObjectSetting(code = "dateEditFlag")
    private Integer dateEditFlag;
    /**
     * TAT
     */
    @ObjectSetting(code = "tat")
    private Integer tat;
    /**
     * Order Expect DueDate
     */
    @ObjectSetting(code = "orderExpectDueDate")
    private Date expectedOrderDueDate;
    /**
     *
     */
    @ObjectSetting(code = "actualStartDate")
    private Date actualStartDate;
    /**
     *
     */

    private Date serviceStartDate;
    /**
     *
     */
    @ObjectSetting(code = "remark")
    private String remark;
    /**
     * Order Create Date
     */
    private Date createdDate;

    private Date modifitedDate;
    /**
     * Order Confirm Date
     */
    @ObjectSetting(code = "orderConfirmDate")
    private Date orderConfirmDate;
    /**
     * TO DM->Customer Department
     */
    private String kACustomerDeptCode;
    /**
     * Reference Order
     */
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String oldOrderNo;
    private String externalOldOrderNo;

    /**
     *
     */
    private Long organizationId;
    /**
     * OrganizationName VARCHAR(200)<br>
     *
     */
    private String organizationName;
    /**
     *
     */
    private String legalEntityCode;
    /**
     *
     */
    private String legalEntityName;
    /**
     * operationType INTEGER(10)<br>
     *
     */
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private Integer operationType;
    /**
     *
     */
    private String operationTypeDesc;
    /**
     * RefReportNo VARCHAR(50)<br>
     * 获得 Amend By Report时, 记录reportNo
     */
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refReportNo;
    // endregion

    // region 【tb_sl_order】
    /**
     *
     */
    private String slOrderId;
    /**
     * GENERAL INFO-》TRF Number
     */
    private String trfNo;

    /**
     *
     * 第三方进单单号
     */
    private String referenceNo;
    private String referenceOrderNo;

    private List<ReferenceInfo> references;
    /**
     * 第三方id
     */
//    private Integer referenceId;

    /**
     * BOSS Order Number
     */
    private String bossOrderNumber;
    /**
     * Responsible CS
     */
    @ObjectSetting(code = "cSName")
    private String cSName;
    /**
     * CS Contact
     */
    @ObjectSetting(code = "cSContact")
    private String cSContact;
    /**
     * Responsible Team
     */
    @ObjectSetting(code = "responsibleTeamCode")
    private String responsibleTeamCode;
    /**
     * CS Email
     */
    @ObjectSetting(code = "cSEmail")
    private String cSEmail;
    /**
     *
     */
    @ObjectSetting(code = "customerRemark")
    private String customerRemark;
    /**
     * Sample Confirm Date
     */
    @ObjectSetting(code = "sampleConfirmDate")
    private Date sampleConfirmDate;
    /**
     * Sample Receive Date
     */
    @ObjectSetting(code = "sampleReceiveDate")
    private Date sampleReceiveDate;
    /**
     * Cutting Expect DueDate
     */
    @ObjectSetting(code = "cuttingExpectDueDate")
    private Date cuttingExpectDueDate;
    /**
     * Report Expect DueDate
     */
    @ObjectSetting(code = "reportExpectDueDate")
    private Date reportExpectDueDate;
    /**
     * Job Expect DueDate
     */
    @ObjectSetting(code = "jobExpectDueDate")
    private Date jobExpectDueDate;
    
    private Date subcontractExpectDueDate;
    /**
     * TRF Submission Date
     */
    @ObjectSetting(code = "tRFSubmissionDate")
    private Date tRFSubmissionDate;
    /**
     * Customer Reference No
     */
    @ObjectSetting(code = "customerRefNo")
    private String customerRefNo;
    /**
     * OR
     */
    @ObjectSetting(code = "or")
    private String or;
    /**
     * CR
     */
    @ObjectSetting(code = "cr")
    private String cr;
    /**
     *
     */
    @ObjectSetting(code = "orderCategory")
    private Integer orderCategory;
    /**
     *
     */
    @ObjectSetting(code = "caseType")
    private String caseType;
    /**
     * sl 表 新增字段 2020年3月26日
     */
    @ObjectSetting(code = "idbLab")
    private String idbLab;
    /**
     * TODO Testing for self reference
     * 是否是自测单
     */
    @ObjectSetting(code = "testingReference")
    private Integer selfTestFlag;
    /**
     *
     */
    private int toBossFlag;
    /**
     *
     */
    private Integer toTestFlag;
    /**
     *
     */
    private Integer toDMFlag;
    /**
     *
     */
    private Integer pendingFlag;
    /**
     *
     */
    private Integer isCopyOrderFlag;
    /**
     *
     */
    @ObjectSetting(code = "bossOrderNo")
    private String bossOrderNo;
    /**
     *
     */
    @ObjectSetting(code = "delayType")
    private String delayType;
    /**
     *
     */
    private String delayTypeValue;

    @ObjectSetting(code = "delayReason")
    private String delayReason;

    private String suffixNum;

    /**
     * SpecialDiscount DECIMAL(18,5)<br>
     * record the order splecial discount
     */
    private BigDecimal specialDiscount;

    /**
     * QuoteServiceLevel INTEGER(10)<br>
     *
     */
    private Integer quoteServiceLevel;
    /**
     * QuoteCurrencyID VARCHAR(10)<br>
     *
     */
    private String quoteCurrencyId;
    /**
     * charging_status INTEGER(10) 默认值[0]<br>
     * Quoted:2,Invoiced:4
     */
    private Integer charging_status;
    /**
     * CustomerReference VARCHAR(500)<br>
     *
     */
    private String customerReference;
    /**
     *
     */
    @ObjectSetting(code = "tracingFlag")
    private Integer tracingFlag;
    // endregion

    /**
     * testRequest-》Service Type
     */
    private int serviceType;
    /**
     * orderParcel-》Parcel NO
     */
    @ObjectSetting(code = "parcelNo")
    private List<String> parcelNo;
    /**
     * orderParcel-》Parcel NO
     */
    private List<String> oldParcelNo;
    /**
     *
     */
    private String postfix;
    /**
     *
     */
    private String[] postfixItems;
    /**
     *
     */
    private String toLab;
    private String shareToLab;
    private boolean isShare;
    private boolean topsToDisabled = false;
    // 配置为空表示有权限
    private String blockTops;
    /**
     *
     */
    private String trfTemplateOwner;

    private boolean bindStatus;

    private boolean unbindStatus;

    private boolean trfBindStatus;

    private boolean trfUnbindStatus;

    private Integer operationMode;

    @ObjectSetting(code = "enquiryProductCategory")
    private String[] productCategories;
    private String productCategory;
    private String productSubCategory;

    @ObjectSetting(code = "paymentStatus")
    private Integer paymentStatus;

    @ObjectSetting(code = "refBossInvoiceNo")
    private String refBossInvoiceNo;

    @ObjectSetting(code = "starlimsFolderNo")
    private String starlimsFolderNo;


    /**
     * enquiry 相关字段
     */
    private String enquiryNo;

    private String enquiryId;

    private String groupId;


    @ObjectSetting(code="sampleInfoReceived")
    private Date furtherInformationReceivingDate;

    @ObjectSetting(code = "sampleResubmission")
    private Date sampleResubmissionDate;

    private Date parcelReceiveDate;

    private String salesPerson;

    private String tsPerson;

    @ObjectSetting(code = "project")
    private String project;

    @ObjectSetting(code = "ka")
    private String KA;

    /**
     * BOSS Order Number
     */
    private String bossOrderNumberHtml;

    /**
     * 动态 tag value JSON
     */
    private String tagValueJson;

    /**
     *
     * @return
     */
    private List<TagValueSelectDTO> tagValueSelectDTOS;

    private String matrixNo;
    @ObjectSetting(code = "certificateProgram")
    private String certificateProgram;

    private Integer toStarLims;

    /**
     * reportInfo是否同步为Applicant信息 0-否 1-是
     */
    private Integer sameAsApplicantFlag;

    private String subcontractFeeCurrency;
    private BigDecimal subcontractFee;
    private BigDecimal mainCurrencySubcontractFee;
    private String otsSubcontractNo;
    // 版本ID控制并发
    private Long versionId;
    // TO CP Customer Portal Reference Number
    private String protoReferenceNo;
    private Integer refSystemId;
    private Integer setDueDateFlag;

    private Boolean isFreeQuotation;

    private String hostDffFormId;

    private String hostDffGridId;
    private Integer needToBossFlag;
    private String rootBuCode;
    private String todoId;
    private Boolean completeTodo;
    private Integer dueDateConfirmFlag;

    private String templateId;

    private String templateName;

    private String orderSource;

    private Boolean syncDueDate = true;

    private String costLocation;

    private String costCenter;

    public Boolean getSyncDueDate() {
        return syncDueDate;
    }

    public void setSyncDueDate(Boolean syncDueDate) {
        this.syncDueDate = syncDueDate;
    }

    private Integer systemId = SgsSystem.GPO.getSgsSystemId();

    public String getTodoId() {
        return todoId;
    }

    public void setTodoId(String todoId) {
        this.todoId = todoId;
    }

    public String getRootBuCode() {
        return rootBuCode;
    }

    public void setRootBuCode(String rootBuCode) {
        this.rootBuCode = rootBuCode;
    }

    public Integer getNeedToBossFlag() {
        return needToBossFlag;
    }

    public void setNeedToBossFlag(Integer needToBossFlag) {
        this.needToBossFlag = needToBossFlag;
    }

    public String getHostDffFormId() {
        return hostDffFormId;
    }

    public void setHostDffFormId(String hostDffFormId) {
        this.hostDffFormId = hostDffFormId;
    }

    public String getHostDffGridId() {
        return hostDffGridId;
    }

    public void setHostDffGridId(String hostDffGridId) {
        this.hostDffGridId = hostDffGridId;
    }

    public List<ReferenceInfo> getReferences() {
        return references;
    }

    public void setReferences(List<ReferenceInfo> references) {
        this.references = references;
    }

    public Boolean getFreeQuotation() {
        return isFreeQuotation;
    }

    public void setFreeQuotation(Boolean freeQuotation) {
        isFreeQuotation = freeQuotation;
    }

    public Integer getSetDueDateFlag() {
        return setDueDateFlag;
    }

    public void setSetDueDateFlag(Integer setDueDateFlag) {
        this.setDueDateFlag = setDueDateFlag;
    }

    public Integer getRefSystemId() {
        return refSystemId;
    }

    public void setRefSystemId(Integer refSystemId) {
        this.refSystemId = refSystemId;
    }

    public String getProtoReferenceNo() {
        return protoReferenceNo;
    }

    public void setProtoReferenceNo(String protoReferenceNo) {
        this.protoReferenceNo = protoReferenceNo;
    }

    public String getShareToLab() {
        return shareToLab;
    }

    public void setShareToLab(String shareToLab) {
        this.shareToLab = shareToLab;
    }

    public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }

    public String getOtsSubcontractNo() {
        return otsSubcontractNo;
    }

    public void setOtsSubcontractNo(String otsSubcontractNo) {
        this.otsSubcontractNo = otsSubcontractNo;
    }

    public String getSubcontractFeeCurrency() {
        return subcontractFeeCurrency;
    }

    public void setSubcontractFeeCurrency(String subcontractFeeCurrency) {
        this.subcontractFeeCurrency = subcontractFeeCurrency;
    }

    public BigDecimal getSubcontractFee() {
        return subcontractFee;
    }

    public void setSubcontractFee(BigDecimal subcontractFee) {
        this.subcontractFee = subcontractFee;
    }

    public BigDecimal getMainCurrencySubcontractFee() {
        return mainCurrencySubcontractFee;
    }

    public void setMainCurrencySubcontractFee(BigDecimal mainCurrencySubcontractFee) {
        this.mainCurrencySubcontractFee = mainCurrencySubcontractFee;
    }

    public Integer getSameAsApplicantFlag() {
        return sameAsApplicantFlag;
    }

    public void setSameAsApplicantFlag(Integer sameAsApplicantFlag) {
        this.sameAsApplicantFlag = sameAsApplicantFlag;
    }

    public Integer getToStarLims() {
        return toStarLims;
    }

    public void setToStarLims(Integer toStarLims) {
        this.toStarLims = toStarLims;
    }

    public String getCertificateProgram() {
        return certificateProgram;
    }

    public void setCertificateProgram(String certificateProgram) {
        this.certificateProgram = certificateProgram;
    }

    public String getProductSubCategory() {
        return productSubCategory;
    }

    public void setProductSubCategory(String productSubCategory) {
        this.productSubCategory = productSubCategory;
    }

    public String getMatrixNo() {
        return matrixNo;
    }

    public void setMatrixNo(String matrixNo) {
        this.matrixNo = matrixNo;
    }

    public List<TagValueSelectDTO> getTagValueSelectDTOS() {
        return tagValueSelectDTOS;
    }

    public void setTagValueSelectDTOS(List<TagValueSelectDTO> tagValueSelectDTOS) {
        this.tagValueSelectDTOS = tagValueSelectDTOS;
    }

    public String getTagValueJson() {
        return tagValueJson;
    }

    public void setTagValueJson(String tagValueJson) {
        this.tagValueJson = tagValueJson;
    }

    public String getSuffixNum() {
        return suffixNum;
    }

    public void setSuffixNum(String suffixNum) {
        this.suffixNum = suffixNum;
    }

    public String getBossOrderNumberHtml() {
        return bossOrderNumberHtml;
    }

    public void setBossOrderNumberHtml(String bossOrderNumberHtml) {
        this.bossOrderNumberHtml = bossOrderNumberHtml;
    }

    public String getProductCategory() {
        return productCategory;
    }

    public void setProductCategory(String productCategory) {
        this.productCategory = productCategory;
    }

    public Integer getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(Integer paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getRefBossInvoiceNo() {
        return refBossInvoiceNo;
    }

    public void setRefBossInvoiceNo(String refBossInvoiceNo) {
        this.refBossInvoiceNo = refBossInvoiceNo;
    }

    public String getStarlimsFolderNo() {
        return starlimsFolderNo;
    }

    public void setStarlimsFolderNo(String starlimsFolderNo) {
        this.starlimsFolderNo = starlimsFolderNo;
    }

    public String getEnquiryNo() {
        return enquiryNo;
    }

    public void setEnquiryNo(String enquiryNo) {
        this.enquiryNo = enquiryNo;
    }

    public Date getFurtherInformationReceivingDate() {
        return furtherInformationReceivingDate;
    }

    public void setFurtherInformationReceivingDate(Date furtherInformationReceivingDate) {
        this.furtherInformationReceivingDate = furtherInformationReceivingDate;
    }

    public Date getSampleResubmissionDate() {
        return sampleResubmissionDate;
    }

    public void setSampleResubmissionDate(Date sampleResubmissionDate) {
        this.sampleResubmissionDate = sampleResubmissionDate;
    }

    public String getNewOrderId() {
        return newOrderId;
    }

    public void setNewOrderId(String newOrderId) {
        this.newOrderId = newOrderId;
    }

    public boolean isBindStatus() {
        return bindStatus;
    }

    public void setBindStatus(boolean bindStatus) {
        this.bindStatus = bindStatus;
    }

    public boolean isUnbindStatus() {
        return unbindStatus;
    }

    public void setUnbindStatus(boolean unbindStatus) {
        this.unbindStatus = unbindStatus;
    }

    public Integer getTracingFlag() {
        return tracingFlag;
    }

    public void setTracingFlag(Integer tracingFlag) {
        this.tracingFlag = tracingFlag;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public int getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(int orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getOrderStatusText() {
        return orderStatusText;
    }

    public void setOrderStatusText(String orderStatusText) {
        this.orderStatusText = orderStatusText;
    }

    public int getOrderType() {
        return orderType;
    }

    public void setOrderType(int orderType) {
        this.orderType = orderType;
    }

    public Integer getServiceLevel() {
        return serviceLevel;
    }

    public void setServiceLevel(Integer serviceLevel) {
        this.serviceLevel = serviceLevel;
    }

    public int getBuId() {
        return buId;
    }

    public void setBuId(int buId) {
        this.buId = buId;
    }

    public String getBuCode() {
        return buCode;
    }

    public void setBuCode(String buCode) {
        this.buCode = buCode;
    }

    public Integer getLabId() {
        return labId;
    }

    public void setLabId(Integer labId) {
        this.labId = labId;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getLabName() {
        return labName;
    }

    public void setLabName(String labName) {
        this.labName = labName;
    }

    public int getLocationId() {
        return locationId;
    }

    public void setLocationId(int locationId) {
        this.locationId = locationId;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public Integer getDateEditFlag() {
        return dateEditFlag;
    }

    public void setDateEditFlag(Integer dateEditFlag) {
        this.dateEditFlag = dateEditFlag;
    }

    public Integer getTat() {
        return tat;
    }

    public void setTat(Integer tat) {
        this.tat = tat;
    }

    public Date getExpectedOrderDueDate() {
        return expectedOrderDueDate;
    }

    public void setExpectedOrderDueDate(Date expectedOrderDueDate) {
        this.expectedOrderDueDate = expectedOrderDueDate;
    }

    public Date getActualStartDate() {
        return actualStartDate;
    }

    public void setActualStartDate(Date actualStartDate) {
        this.actualStartDate = actualStartDate;
    }

    public Date getServiceStartDate() {
        return serviceStartDate;
    }

    public void setServiceStartDate(Date serviceStartDate) {
        this.serviceStartDate = serviceStartDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getModifitedDate() {
        return modifitedDate;
    }

    public void setModifitedDate(Date modifitedDate) {
        this.modifitedDate = modifitedDate;
    }

    public Date getOrderConfirmDate() {
        return orderConfirmDate;
    }

    public void setOrderConfirmDate(Date orderConfirmDate) {
        this.orderConfirmDate = orderConfirmDate;
    }

    public String getOldOrderNo() {
        return oldOrderNo;
    }

    public void setOldOrderNo(String oldOrderNo) {
        this.oldOrderNo = oldOrderNo;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getOperationTypeDesc() {
        return operationTypeDesc;
    }

    public void setOperationTypeDesc(String operationTypeDesc) {
        this.operationTypeDesc = operationTypeDesc;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public String getRefReportNo() {
        return refReportNo;
    }

    public void setRefReportNo(String refReportNo) {
        this.refReportNo = refReportNo;
    }

    public String getSlOrderId() {
        return slOrderId;
    }

    public void setSlOrderId(String slOrderId) {
        this.slOrderId = slOrderId;
    }

    public String getTrfNo() {
        return trfNo;
    }

    public void setTrfNo(String trfNo) {
        this.trfNo = trfNo;
    }

    public String getBlockTops() {
        return blockTops;
    }

    public void setBlockTops(String blockTops) {
        this.blockTops = blockTops;
    }

    public String getBossOrderNumber() {
        return bossOrderNumber;
    }

    public void setBossOrderNumber(String bossOrderNumber) {
        this.bossOrderNumber = bossOrderNumber;
    }

    public String getcSName() {
        return cSName;
    }

    public void setcSName(String cSName) {
        this.cSName = cSName;
    }

    public String getcSContact() {
        return cSContact;
    }

    public void setcSContact(String cSContact) {
        this.cSContact = cSContact;
    }

    public String getResponsibleTeamCode() {
        return responsibleTeamCode;
    }

    public void setResponsibleTeamCode(String responsibleTeamCode) {
        this.responsibleTeamCode = responsibleTeamCode;
    }

    public String getcSEmail() {
        return cSEmail;
    }

    public void setcSEmail(String cSEmail) {
        this.cSEmail = cSEmail;
    }

    public String getCustomerRemark() {
        return customerRemark;
    }

    public void setCustomerRemark(String customerRemark) {
        this.customerRemark = customerRemark;
    }

    public Date getSampleConfirmDate() {
        return sampleConfirmDate;
    }

    public void setSampleConfirmDate(Date sampleConfirmDate) {
        this.sampleConfirmDate = sampleConfirmDate;
    }

    public Date getCuttingExpectDueDate() {
        return cuttingExpectDueDate;
    }

    public void setCuttingExpectDueDate(Date cuttingExpectDueDate) {
        this.cuttingExpectDueDate = cuttingExpectDueDate;
    }

    public Date getReportExpectDueDate() {
        return reportExpectDueDate;
    }

    public void setReportExpectDueDate(Date reportExpectDueDate) {
        this.reportExpectDueDate = reportExpectDueDate;
    }

    public Date getJobExpectDueDate() {
        return jobExpectDueDate;
    }

    public void setJobExpectDueDate(Date jobExpectDueDate) {
        this.jobExpectDueDate = jobExpectDueDate;
    }

    public Date gettRFSubmissionDate() {
        return tRFSubmissionDate;
    }

    public void settRFSubmissionDate(Date tRFSubmissionDate) {
        this.tRFSubmissionDate = tRFSubmissionDate;
    }

    public String getCustomerRefNo() {
        return customerRefNo;
    }

    public void setCustomerRefNo(String customerRefNo) {
        this.customerRefNo = customerRefNo;
    }

    public String getOr() {
        return or;
    }

    public void setOr(String or) {
        this.or = or;
    }

    public String getCr() {
        return cr;
    }

    public void setCr(String cr) {
        this.cr = cr;
    }

    public Integer getOrderCategory() {
        return orderCategory;
    }

    public void setOrderCategory(Integer orderCategory) {
        this.orderCategory = orderCategory;
    }

    public String getCaseType() {
        return caseType;
    }

    public void setCaseType(String caseType) {
        this.caseType = caseType;
    }

    public Integer getSelfTestFlag() {
        return selfTestFlag;
    }

    public void setSelfTestFlag(Integer selfTestFlag) {
        this.selfTestFlag = selfTestFlag;
    }

    public int getToBossFlag() {
        return toBossFlag;
    }

    public void setToBossFlag(int toBossFlag) {
        this.toBossFlag = toBossFlag;
    }

    public Integer getToTestFlag() {
        return toTestFlag;
    }

    public void setToTestFlag(Integer toTestFlag) {
        this.toTestFlag = toTestFlag;
    }

    public Integer getToDMFlag() {
        return toDMFlag;
    }

    public void setToDMFlag(Integer toDMFlag) {
        this.toDMFlag = toDMFlag;
    }

    public Integer getPendingFlag() {
        return pendingFlag;
    }

    public void setPendingFlag(Integer pendingFlag) {
        this.pendingFlag = pendingFlag;
    }

    public Integer getIsCopyOrderFlag() {
        return isCopyOrderFlag;
    }

    public void setIsCopyOrderFlag(Integer isCopyOrderFlag) {
        this.isCopyOrderFlag = isCopyOrderFlag;
    }

    public String getBossOrderNo() {
        return bossOrderNo;
    }

    public void setBossOrderNo(String bossOrderNo) {
        this.bossOrderNo = bossOrderNo;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public String getLegalEntityCode() {
        return legalEntityCode;
    }

    public void setLegalEntityCode(String legalEntityCode) {
        this.legalEntityCode = legalEntityCode;
    }

    public String getLegalEntityName() {
        return legalEntityName;
    }

    public void setLegalEntityName(String legalEntityName) {
        this.legalEntityName = legalEntityName;
    }

    public String getDelayType() {
        return delayType;
    }

    public void setDelayType(String delayType) {
        this.delayType = delayType;
    }

    public String getDelayReason() {
        return delayReason;
    }

    public void setDelayReason(String delayReason) {
        this.delayReason = delayReason;
    }

    public int getServiceType() {
        return serviceType;
    }

    public void setServiceType(int serviceType) {
        this.serviceType = serviceType;
    }

    public List<String> getParcelNo() {
        return parcelNo;
    }

    public void setParcelNo(List<String> parcelNo) {
        this.parcelNo = parcelNo;
    }

    public List<String> getOldParcelNo() {
        return oldParcelNo;
    }

    public void setOldParcelNo(List<String> oldParcelNo) {
        this.oldParcelNo = oldParcelNo;
    }

    public String getkACustomerDeptCode() {
        return kACustomerDeptCode;
    }

    public void setkACustomerDeptCode(String kACustomerDeptCode) {
        this.kACustomerDeptCode = kACustomerDeptCode;
    }

    public String getPostfix() {
        return postfix;
    }

    public void setPostfix(String postfix) {
        this.postfix = postfix;
    }

    public String[] getPostfixItems() {
        return postfixItems;
    }

    public void setPostfixItems(String[] postfixItems) {
        this.postfixItems = postfixItems;
    }

    public BigDecimal getSpecialDiscount() {
        return specialDiscount;
    }

    public void setSpecialDiscount(BigDecimal specialDiscount) {
        this.specialDiscount = specialDiscount;
    }

    public Integer getQuoteServiceLevel() {
        return quoteServiceLevel;
    }

    public void setQuoteServiceLevel(Integer quoteServiceLevel) {
        this.quoteServiceLevel = quoteServiceLevel;
    }

    public String getQuoteCurrencyId() {
        return quoteCurrencyId;
    }

    public void setQuoteCurrencyId(String quoteCurrencyId) {
        this.quoteCurrencyId = quoteCurrencyId;
    }

    public Integer getCharging_status() {
        return charging_status;
    }

    public void setCharging_status(Integer charging_status) {
        this.charging_status = charging_status;
    }

    public String getCustomerReference() {
        return customerReference;
    }

    public void setCustomerReference(String customerReference) {
        this.customerReference = customerReference;
    }

	public Date getSubcontractExpectDueDate() {
		return subcontractExpectDueDate;
	}

	public void setSubcontractExpectDueDate(Date subcontractExpectDueDate) {
		this.subcontractExpectDueDate = subcontractExpectDueDate;
	}

    public String getToLab() {
        return toLab;
    }

    public void setToLab(String toLab) {
        this.toLab = toLab;
    }

    public String getTrfTemplateOwner() {
        return trfTemplateOwner;
    }

    public void setTrfTemplateOwner(String trfTemplateOwner) {
        this.trfTemplateOwner = trfTemplateOwner;
    }


    public String getIdbLab() {
        return idbLab;
    }

    public void setIdbLab(String idbLab) {
        this.idbLab = idbLab;
    }

    public Integer getOperationMode() {
        return operationMode;
    }

    public void setOperationMode(Integer operationMode) {
        this.operationMode = operationMode;
    }

    public int getSubcontractOrderFlag() {
        return subcontractOrderFlag;
    }

    public void setSubcontractOrderFlag(int subcontractOrderFlag) {
        this.subcontractOrderFlag = subcontractOrderFlag;
    }

    public String getSalesPerson() {
        return salesPerson;
    }

    public void setSalesPerson(String salesPerson) {
        this.salesPerson = salesPerson;
    }

    public String getTsPerson() {
        return tsPerson;
    }

    public void setTsPerson(String tsPerson) {
        this.tsPerson = tsPerson;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getKA() {
        return KA;
    }

    public void setKA(String KA) {
        this.KA = KA;
    }

    public String getEnquiryId() {
        return enquiryId;
    }

    public void setEnquiryId(String enquiryId) {
        this.enquiryId = enquiryId;
    }

    public Boolean getCompleteTodo() {
        return completeTodo;
    }

    public void setCompleteTodo(Boolean completeTodo) {
        this.completeTodo = completeTodo;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String[] getProductCategories() {
        return productCategories;
    }

    public void setProductCategories(String[] productCategories) {
        this.productCategories = productCategories;
    }

    public Date getSampleReceiveDate() {
        return sampleReceiveDate;
    }

    public void setSampleReceiveDate(Date sampleReceiveDate) {
        this.sampleReceiveDate = sampleReceiveDate;
    }


    public Date getParcelReceiveDate() {
        return parcelReceiveDate;
    }

    public void setParcelReceiveDate(Date parcelReceiveDate) {
        this.parcelReceiveDate = parcelReceiveDate;
    }


    public String getExternalOrderNo() {
        return externalOrderNo;
    }

    public void setExternalOrderNo(String externalOrderNo) {
        this.externalOrderNo = externalOrderNo;
    }


    public boolean isTrfBindStatus() {
        return trfBindStatus;
    }

    public void setTrfBindStatus(boolean trfBindStatus) {
        this.trfBindStatus = trfBindStatus;
    }

    public boolean isTrfUnbindStatus() {
        return trfUnbindStatus;
    }

    public void setTrfUnbindStatus(boolean trfUnbindStatus) {
        this.trfUnbindStatus = trfUnbindStatus;
    }

    public String getExternalOldOrderNo() {
        return externalOldOrderNo;
    }

    public void setExternalOldOrderNo(String externalOldOrderNo) {
        this.externalOldOrderNo = externalOldOrderNo;
    }

    public boolean isShare() {
        return isShare;
    }

    public void setShare(boolean share) {
        isShare = share;
    }

    public boolean isTopsToDisabled() {
        return topsToDisabled;
    }

    public void setTopsToDisabled(boolean topsToDisabled) {
        this.topsToDisabled = topsToDisabled;
    }

    public Integer getDueDateConfirmFlag() {
        return dueDateConfirmFlag;
    }

    public void setDueDateConfirmFlag(Integer dueDateConfirmFlag) {
        this.dueDateConfirmFlag = dueDateConfirmFlag;
    }

    public String getTemplateId() { return templateId;}

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public Integer getSystemId() {
        return systemId;
    }

    public void setSystemId(Integer systemId) {
        this.systemId = systemId;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public String getDelayTypeValue() {
        return delayTypeValue;
    }

    public void setDelayTypeValue(String delayTypeValue) {
        this.delayTypeValue = delayTypeValue;
    }
    /*public String getReferenceOrderNo() {
        return referenceOrderNo;
    }

    public void setReferenceOrderNo(String referenceOrderNo) {
        this.referenceOrderNo = referenceOrderNo;
    }*/

    /**
     *
     * @param other
     * @return
     */
//    public int hashCode(OrderDetailOtherInfo other) {
//        if(Objects.isNull(other)){
//            other = new OrderDetailOtherInfo();
//        }
//        final int prime = 31;
//        int hashCode = 1;
//
//        hashCode = prime * hashCode + (StringUtil.hashCode(orderId));
//        hashCode = prime * hashCode + (StringUtil.hashCode(orderNo));
//        hashCode = prime * hashCode + (serviceLevel);
//        hashCode = prime * hashCode + (selfTestFlag);
//        hashCode = prime * hashCode + (locationId);
//        hashCode = prime * hashCode + (StringUtil.hashCode(pendingFlag));
//        hashCode = prime * hashCode + (tat);
//
//        hashCode = prime * hashCode + (StringUtil.hashCode(serviceStartDate));
//        hashCode = prime * hashCode + (StringUtil.hashCode(expectedOrderDueDate));
//        hashCode = prime * hashCode + (StringUtil.hashCode(reportExpectDueDate));
//
//        hashCode = prime * hashCode + (StringUtil.hashCode(other.getExpressNo()));
//        hashCode = prime * hashCode + (StringUtil.hashCode(remark));
//        hashCode = prime * hashCode + (StringUtil.hashCode(kACustomerDeptCode));
//        hashCode = prime * hashCode + (StringUtil.hashCode(organizationName));
//
//        return hashCode;
//    }

    public int getOrderHashCode(OrderDetailOtherInfo other){
        if(Objects.isNull(other)){
            other = new OrderDetailOtherInfo();
        }
        final int prime = 31;
        int hashCode = 1;

        hashCode = prime * hashCode + (StringUtil.hashCode(orderId));
        hashCode = prime * hashCode + (StringUtil.hashCode(orderNo));
        hashCode = prime * hashCode + (StringUtil.hashCode(serviceLevel));
        hashCode = prime * hashCode + (StringUtil.hashCode(orderStatus));
        hashCode = prime * hashCode + (StringUtil.hashCode(buId));
        hashCode = prime * hashCode + (StringUtil.hashCode(buCode));
        hashCode = prime * hashCode + (StringUtil.hashCode(locationId));
        hashCode = prime * hashCode + (StringUtil.hashCode(locationCode));
        hashCode = prime * hashCode + (StringUtil.hashCode(certificateProgram));
//        hashCode = prime * hashCode + (StringUtil.hashCode(toBossFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(paymentStatus));
        hashCode = prime * hashCode + (StringUtil.hashCode(isCopyOrderFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(selfTestFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(kACustomerDeptCode));
        hashCode = prime * hashCode + (StringUtil.hashCode(tat));
        hashCode = prime * hashCode + (StringUtil.hashCode(expectedOrderDueDate));
        hashCode = prime * hashCode + (StringUtil.hashCode(reportExpectDueDate));
        hashCode = prime * hashCode + (StringUtil.hashCode(sampleReceiveDate));
//        hashCode = prime * hashCode + (StringUtil.hashCode(serviceStartDate));
        hashCode = prime * hashCode + (StringUtil.hashCode(remark));
        hashCode = prime * hashCode + (StringUtil.hashCode(orderConfirmDate));
        hashCode = prime * hashCode + (StringUtil.hashCode(oldOrderNo));
        hashCode = prime * hashCode + (StringUtil.hashCode(operationType));
        hashCode = prime * hashCode + (StringUtil.hashCode(organizationName));
        hashCode = prime * hashCode + (StringUtil.hashCode(toDMFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(operationMode));
//        hashCode = prime * hashCode + (StringUtil.hashCode(tracingFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(other.getExpressNo()));
        hashCode = prime * hashCode + (StringUtil.hashCode(rootBuCode));
        hashCode = prime * hashCode + (StringUtil.hashCode(needToBossFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(modifitedDate));


        if(!CollectionUtils.isEmpty(other.getTagValues())){
            List<TagValueSelectDTO> tagValues = other.getTagValues();
            tagValues = tagValues.stream().sorted(Comparator.comparing(TagValueSelectDTO::getTagId)).collect(Collectors.toList());
            for(TagValueSelectDTO tagValue:tagValues){
                hashCode = prime * hashCode + (StringUtil.hashCode(tagValue.getTagId()));
                List<String> selectedValueList = tagValue.getSelectedValue();
                if(!CollectionUtils.isEmpty(selectedValueList)){
                    hashCode = prime * hashCode + (StringUtil.hashCode(selectedValueList));
                }
            }
        }
        return hashCode;
    }

    /**
     *
     * @param other
     * @return
     */
    public int getSlOrderHashCode(OrderDetailOtherInfo other) {
        if(other == null ){
            other = new OrderDetailOtherInfo();
        }
        final int prime = 31;
        int hashCode = 1;

        hashCode = prime * hashCode + (StringUtil.hashCode(slOrderId));
        hashCode = prime * hashCode + (StringUtil.hashCode(cSName));
        //hashCode = prime * hashCode + (StringUtil.hashCode(trfNo));
        hashCode = prime * hashCode + (StringUtil.hashCode(responsibleTeamCode));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialDiscount));
        hashCode = prime * hashCode + (StringUtil.hashCode(cuttingExpectDueDate));
//        hashCode = prime * hashCode + (StringUtil.hashCode(jobExpectDueDate));
        hashCode = prime * hashCode + (StringUtil.hashCode(subcontractExpectDueDate));

//        hashCode = prime * hashCode + (StringUtil.hashCode(paymentStatus));
        hashCode = prime * hashCode + (StringUtil.hashCode(customerRemark));
        hashCode = prime * hashCode + (StringUtil.hashCode(dateEditFlag));
//        hashCode = prime * hashCode + (StringUtil.hashCode(other.getSoftCopyDeliveryDate()));
//        hashCode = prime * hashCode + (StringUtil.hashCode(other.getHardCopyDeliveryDate()));
        hashCode = prime * hashCode + (StringUtil.hashCode(caseType));
//        hashCode = prime * hashCode + (StringUtil.hashCode(other.getReportEmailBy()));
//        hashCode = prime * hashCode + (StringUtil.hashCode(other.getHardcopyDeliveryBy()));
//        hashCode = prime * hashCode + (StringUtil.hashCode(other.getDeliveryRemark()));

        hashCode = prime * hashCode + (StringUtil.hashCode(delayType));
        hashCode = prime * hashCode + (StringUtil.hashCode(delayReason));
        hashCode = prime * hashCode + (StringUtil.hashCode(cSContact));
        hashCode = prime * hashCode + (StringUtil.hashCode(cSEmail));
        hashCode = prime * hashCode + (StringUtil.hashCode(or));
        hashCode = prime * hashCode + (StringUtil.hashCode(cr));

        hashCode = prime * hashCode + (StringUtil.hashCode(sampleConfirmDate));
        hashCode = prime * hashCode + (StringUtil.hashCode(bossOrderNo));
        hashCode = prime * hashCode + (StringUtil.hashCode(customerRefNo));
        //hashCode = prime * hashCode + (StringUtil.hashCode(tracingFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(trfTemplateOwner));
//        hashCode = prime * hashCode + (StringUtil.hashCode(referenceNo));
//        hashCode = prime * hashCode + (StringUtil.hashCode(referenceId));

        hashCode = prime * hashCode + (StringUtil.hashCode(salesPerson));
//        hashCode = prime * hashCode + (StringUtil.hashCode(tsPerson));
        hashCode = prime * hashCode + (StringUtil.hashCode(project));
        hashCode = prime * hashCode + (StringUtil.hashCode(KA));
        hashCode = prime * hashCode + (StringUtil.hashCode(sameAsApplicantFlag));
        if(null!=productCategories&&productCategories.length>0){
            hashCode = prime * hashCode + (StringUtil.hashCode(Arrays.asList(productCategories)));
        }
        hashCode = prime * hashCode + (StringUtil.hashCode(sampleResubmissionDate));
        hashCode = prime * hashCode + (StringUtil.hashCode(furtherInformationReceivingDate));
        hashCode = prime * hashCode + (StringUtil.hashCode(starlimsFolderNo));
        if(null != subcontractFee){
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            hashCode = prime * hashCode + (StringUtil.hashCode(decimalFormat.format(subcontractFee)));
        }
        if(null != mainCurrencySubcontractFee){
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            hashCode = prime * hashCode + (StringUtil.hashCode(decimalFormat.format(mainCurrencySubcontractFee)));
        }
        hashCode = prime * hashCode + (StringUtil.hashCode(subcontractFeeCurrency));
        hashCode = prime * hashCode + (StringUtil.hashCode(refBossInvoiceNo));
        return hashCode;
    }

    /**
     *
     * @return
     */
    public int getParcelHashCode(){
        final int prime = 31;
        int hashCode = 1;
        hashCode = prime * hashCode + (StringUtil.hashCode(parcelNo));
        return hashCode;
    }

    public int getTrfRelationshipHashCode(){

        final int prime = 31;
        int hashCode = 0;

        if(!CollectionUtils.isEmpty(references)){
            hashCode = 1;
            for(ReferenceInfo reference : references){
                hashCode = prime * hashCode + (StringUtil.hashCode(reference.getTrfId()));
                hashCode = prime * hashCode + (StringUtil.hashCode(reference.getRefSystemId()));
                hashCode = prime * hashCode + (StringUtil.hashCode(reference.getReferenceNo()));
                hashCode = prime * hashCode + (StringUtil.hashCode(reference.getTrfSourceType()));
                hashCode = prime * hashCode + (StringUtil.hashCode(reference.getIntegrationChannel()));
            }
        }
        return hashCode;
    }

    public String getCostLocation() {
        return costLocation;
    }

    public void setCostLocation(String costLocation) {
        this.costLocation = costLocation;
    }

    public String getCostCenter() {
        return costCenter;
    }

    public void setCostCenter(String costCenter) {
        this.costCenter = costCenter;
    }

    @Override
    public String toString(){
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append(", orderId = ").append(orderId);
        sb.append(", orderNo = ").append(orderNo);
        sb.append(", serviceLevel = ").append(serviceLevel);
        sb.append(", orderStatus = ").append(orderStatus);
        sb.append(", buId = ").append(buId);
        sb.append(", locationId = ").append(locationId);
        sb.append(", locationCode = ").append(locationCode);
        sb.append(", certificateProgram = ").append(certificateProgram);
        sb.append(", paymentStatus = ").append(paymentStatus);
        sb.append(", isCopyOrderFlag = ").append(isCopyOrderFlag);
        sb.append(", selfTestFlag = ").append(selfTestFlag);
        sb.append(", kACustomerDeptCode = ").append(kACustomerDeptCode);
        sb.append(", tat = ").append(tat);
        sb.append(", expectedOrderDueDate = ").append(expectedOrderDueDate);
        sb.append(", reportExpectDueDate = ").append(reportExpectDueDate);
        sb.append(", sampleReceiveDate = ").append(sampleReceiveDate);
        sb.append(", remark = ").append(remark);
        sb.append(", orderConfirmDate = ").append(orderConfirmDate);
        sb.append(", oldOrderNo = ").append(oldOrderNo);
        sb.append(", operationType = ").append(operationType);
        sb.append(", organizationName = ").append(organizationName);
        sb.append(", toDMFlag = ").append(toDMFlag);
        sb.append(", operationMode = ").append(operationMode);
        sb.append(", orderSource = ").append(orderSource);
        sb.append("]");
        return sb.toString();
    }

//    public int getTopsHashCode(){
//        final int prime = 31;
//        int hashCode = 1;
//        hashCode = prime * hashCode + (StringUtil.strToHash(toLab));
//        return hashCode;
//    }
}
