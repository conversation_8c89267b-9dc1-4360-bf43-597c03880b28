package com.sgs.preorder.facade.model.report.info;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class ReportDetailInfo implements Serializable {

    private static final long serialVersionUID = 2718887310502517137L;

    private String customerId;

    private String applicantEmailAddress;

    private String buyerEmailAddress;

    private String payerEmailAddress;

    private String from;

    private String sender;

    private String respCs;

    private String respCsName;

    private Map<String, String> generalFields;

    private Map<String, String> dffFields;

    private List<Map<String, String>> attachmentList;

}
