package com.sgs.preorder.facade.model.enums;

public enum ReportInfoLanguageEnum {

    CN(1, "CN"),
    EN(0, "EN");

    private Integer status;
    private String value;

    ReportInfoLanguageEnum(Integer status, String value) {
        this.status = status;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public Integer getStatus() {
        return status;
    }
}
