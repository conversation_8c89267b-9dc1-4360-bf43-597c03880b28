package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum CopyDeliverType {
    None(0, "None"),
    Applicant(1, "Applicant"),
    Payer(2, "Payer"),
    Others(5, "Others");

    private final int status;
    private final String code;

    CopyDeliverType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return code;
    }

    public static final Map<String, CopyDeliverType> maps = new HashMap<String, CopyDeliverType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (CopyDeliverType contactsType : CopyDeliverType.values()) {
                put(contactsType.getCode(), contactsType);
            }
        }
    };

    public static CopyDeliverType getCode(String code) {
        if (code == null || code.length() == 0 || !maps.containsKey(code)) {
            return CopyDeliverType.None;
        }
        return maps.get(code);
    }
}
