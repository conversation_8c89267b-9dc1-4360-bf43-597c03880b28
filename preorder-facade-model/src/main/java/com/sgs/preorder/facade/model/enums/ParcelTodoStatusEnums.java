package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ParcelTodoStatusEnums {
    New(1, "New"),
    Delivered(98, "Delivered"),
    Cancel(99, "Cancel");

    private Integer status;
    private String message;

    ParcelTodoStatusEnums(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, ParcelTodoStatusEnums> maps = new HashMap<Integer, ParcelTodoStatusEnums>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ParcelTodoStatusEnums enu : ParcelTodoStatusEnums.values()) {
                put(enu.status, enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }
}
