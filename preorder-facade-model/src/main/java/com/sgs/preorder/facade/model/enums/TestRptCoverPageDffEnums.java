package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: mingyang.chen
 * @Date: 2021/1/20 17:10
 */
public enum TestRptCoverPageDffEnums {
    /**
     * 默认值
     */
    None("None"),
    FirstTimeApplicationFlag("FirstTimeApplicationFlag"),
    LotNo("LotNo"),
    CountryOfOrigin("CountryOfOrigin"),
    CountryOfDestination("CountryOfDestination"),
    ;

    private final String name;

    TestRptCoverPageDffEnums(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
    public static final Map<String, TestRptCoverPageDffEnums> MAPS = new HashMap<>();

    static {
        for (TestRptCoverPageDffEnums enu : values()) {
            MAPS.put(enu.getName(),enu);
        }
    }

    public static TestRptCoverPageDffEnums getInstance(String name) {
        return Optional.ofNullable(MAPS.get(name)).orElse(None);
    }

}
