package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum OperationModeEnums {
    FULL_CYCLE(1, "Full Cycle"),
    TESTING_ONLY(2, "Testing Only"),
    TRACKING_ONLY(4, "Tracing Only"),
    CHARGE_ONLY(8, "Charge Only");

    private Integer status;
    private String message;

    OperationModeEnums(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, OperationModeEnums> maps = new HashMap<Integer, OperationModeEnums>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (OperationModeEnums enu : OperationModeEnums.values()) {
                put(enu.status, enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }
}
