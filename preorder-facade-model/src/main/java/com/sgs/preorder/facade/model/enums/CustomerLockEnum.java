package com.sgs.preorder.facade.model.enums;

public enum CustomerLockEnum {

    LOCK(1, "LOCK"),
    UN_LOCK(0, "UN_LOCK");

    private Integer status;
    private String value;

    CustomerLockEnum(Integer status, String value) {
        this.status = status;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public Integer getStatus() {
        return status;
    }
}
