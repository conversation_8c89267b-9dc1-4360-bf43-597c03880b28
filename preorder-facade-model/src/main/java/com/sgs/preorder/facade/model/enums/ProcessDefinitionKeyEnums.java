package com.sgs.preorder.facade.model.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum ProcessDefinitionKeyEnums {
	CANCEL_ORDER("cancel_order", "CANCEL_ORDER"),
	REVISE_REPORT("revise_apply", "REVISE_REPORT"),
	DISCOUNT_APPLY("discount_apply", "DISCOUNT_APPLY"),
	DELIVER_APPLY("deliver_apply", "DELIVER_APPLY");
	private String  key;
	private String value;

	ProcessDefinitionKeyEnums(String key, String value) {
		this.key = key;
		this.value = value;
	}

	public String getValue() {
		return value;
	}

	public String getKey() {
		return key;
	}

	public static final Map<String, ProcessDefinitionKeyEnums> maps = new HashMap<String, ProcessDefinitionKeyEnums>() {
		private static final long serialVersionUID = -8986866330615001847L;
		{
			for (ProcessDefinitionKeyEnums enu : ProcessDefinitionKeyEnums.values()) {
				put(enu.getKey(), enu);
			}
		}
	};

	public static boolean check(String key, ProcessDefinitionKeyEnums caseType) {
		if (StringUtils.isBlank(key)) {
			return false;
		}
		return maps.get(key) == caseType;
	}

	public static String getValue(String key) {
		if (StringUtils.isBlank(key)) {
			return null;
		}
		ProcessDefinitionKeyEnums processDefinitionKeyEnums = maps.get(key);
		return processDefinitionKeyEnums == null ? null : processDefinitionKeyEnums.getValue();
	}
}
