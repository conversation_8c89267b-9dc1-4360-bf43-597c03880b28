package com.sgs.preorder.facade.model.enums;

public enum SameAsApplicantEnum {

    TRUE(1, "TRUE"),
    FALSE(0, "FALSE");

    private Integer status;
    private String value;

    SameAsApplicantEnum(Integer status, String value) {
        this.status = status;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public Integer getStatus() {
        return status;
    }
}
