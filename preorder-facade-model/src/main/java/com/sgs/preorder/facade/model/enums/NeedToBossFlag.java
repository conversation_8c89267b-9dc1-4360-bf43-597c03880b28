package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum NeedToBossFlag {
    YES(1, "YES"),
    NO(0, "NO");

    private Integer status;
    private String message;

    NeedToBossFlag(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, NeedToBossFlag> maps = new HashMap<Integer, NeedToBossFlag>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (NeedToBossFlag enu : NeedToBossFlag.values()) {
                put(enu.status, enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }
}
