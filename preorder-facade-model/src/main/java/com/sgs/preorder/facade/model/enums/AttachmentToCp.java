package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum AttachmentToCp {
    NO(0, "No"),
    YES(1,"Yes")
    ;

    private final int type;
    private final String message;

    AttachmentToCp(int type, String message) {
        this.type = type;
        this.message = message;
    }
    public int getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, AttachmentToCp> maps = new HashMap<Integer, AttachmentToCp>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (AttachmentToCp enu : AttachmentToCp.values()) {
                put(enu.getType(), enu);
            }
        }
    };

    public static AttachmentToCp findType(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }

    /**
     *
     * @param status
     * @param type
     * @return
     */
    public static boolean check(Integer status, AttachmentToCp type) {
        if (status == null || !maps.containsKey(status.intValue())){
            return false;
        }
        return maps.get(status.intValue()) == type;
    }

    public boolean check(AttachmentToCp... types){
        if (types == null || types.length <= 0){
            return false;
        }
        for (AttachmentToCp type: types){
            if (this.getType() == type.getType()){
                return true;
            }
        }
        return false;
    }
}
