package com.sgs.preorder.facade.model.enums;

public enum ObjectSettingAttributeEnums {
    serviceRequirement("serviceRequirement", "Service Requirement");
    private String code;
    private String name;

    ObjectSettingAttributeEnums(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
