package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ExpectDueDateType {
    None(0, "None"),
    Report(1, "Report"),
    Job(2, "Job"),
    Order(3, "Order"),
    Cutting(4, "Cutting"),
    SubContract(5, "SubContract"),
    TestLine(6, "TestLine");


    private Integer status;
    private String code;

    ExpectDueDateType(Integer status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<String, ExpectDueDateType> maps = new HashMap<String, ExpectDueDateType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ExpectDueDateType enu : ExpectDueDateType.values()) {
                put(enu.code, enu);
            }
        }
    };

    public static ExpectDueDateType findCode(String code) {
        if (code == null || code.length() <= 0 || !maps.containsKey(code)) {
            return null;
        }
        return maps.get(code);
    }
}
