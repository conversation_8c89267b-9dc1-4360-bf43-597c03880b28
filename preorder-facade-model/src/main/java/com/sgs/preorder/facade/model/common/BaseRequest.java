package com.sgs.preorder.facade.model.common;

import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.io.Serializable;
import java.util.Set;

/**
 *
 * <AUTHOR> on 2019/05/09.
 *
 */
public abstract class BaseRequest implements Serializable {

    private static final long serialVersionUID = -7140385409591586152L;

    private static Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 客户端应用ID
     */
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String appId;

    /**
     * 请求ID
     */
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String requestId;
    /**
     *
     */
    private String productLineCode;
    /**
     *
     */

    public String getAppId() {
        return appId;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getProductLineCode() {
        return productLineCode;
    }

    public void setProductLineCode(String productLineCode) {
        this.productLineCode = productLineCode;
    }

    public void validate() {
        StringBuilder errorMsgs = new StringBuilder();
        Set<ConstraintViolation<BaseRequest>> violations = VALIDATOR.validate(this);
        if (violations != null && violations.size() > 0) {
            for (ConstraintViolation<BaseRequest> violation : violations) {
                errorMsgs.append(violation.getPropertyPath()).append(":").append(violation.getMessage()).append("|");
            }
            throw new IllegalArgumentException(errorMsgs.substring(0, errorMsgs.length() - 1));
        }
    }


    /**
     * 一般请求，requestId不强制必填
     * @return
     */
    public boolean requireRequestId( ) {
        return false;
    }

    /**
     * 老的.net系统的请求都沒有appId
     * @return
     */
    public boolean requireAppId( ) {
        return false;
    }
}
