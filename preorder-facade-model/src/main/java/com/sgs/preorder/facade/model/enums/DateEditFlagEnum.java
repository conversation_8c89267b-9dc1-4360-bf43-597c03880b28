package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum DateEditFlagEnum {
    OFF(0, "关闭"),
    ON(1, "打开");

    private Integer status;
    private String message;

    DateEditFlagEnum(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, DateEditFlagEnum> maps = new HashMap<Integer, DateEditFlagEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (DateEditFlagEnum enu : DateEditFlagEnum.values()) {
                put(enu.status, enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }

    public static boolean check(Integer status, DateEditFlagEnum... dateEditFlagEnums) {
        if (null == status) {
            return false;
        }
        for (DateEditFlagEnum dateEditFlagEnum : dateEditFlagEnums) {
            if (dateEditFlagEnum.getStatus() == status) {
                return true;
            }
        }
        return false;
    }
}
