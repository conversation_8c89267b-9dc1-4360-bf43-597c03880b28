package com.sgs.preorder.facade.model.enums;

/**
 * <AUTHOR>
 * @desc 其他业务系统开单时 对应的source 值 枚举
 * @Date 2020年11月5日
 */
public enum OtherSystemSourceEnums {

    /**
     * sgsMart 建单
     * */
    OLD_SgsMart(1,"oldSgsMart"),
    NEW_SgsMart(2,"newSgsMart"),
    /**
     * olb 淘宝建单
     * */
    OLB_SgsMart(3,"olbSgsMart");

    private Integer source;
    private String desc;
    OtherSystemSourceEnums(Integer source,String desc){
        this.source = source;
        this.desc = desc;
    }

    public Integer getSource() {
        return source;
    }

    public String getDesc() {
        return desc;
    }

    public static OtherSystemSourceEnums getSourceEnumBySource(Integer source){
        if(source==null){
            return null;
        }
        for (OtherSystemSourceEnums enums : OtherSystemSourceEnums.values()) {
            if(enums.source.compareTo(source)==0){
                return enums;
            }
        }
        return null;
    }

    public static boolean check(Integer source, OtherSystemSourceEnums ... compEnum){
        for (OtherSystemSourceEnums enums : compEnum) {
            if(enums.source.compareTo(source)==0){
                return true;
            }
        }
        return false;
    }
}
