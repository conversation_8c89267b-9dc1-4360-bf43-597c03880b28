package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum BossOrderStatus {

	Draft(1, "DRAFT","Effective"),
	NotProcess(2, "NOTPROCESS","Effective"),
	Imported(4, "IMPORTED","Effective"),
	Rejected(9,"REJECTED","Effective"),
	Cancelled(5,"CANCELLED","Invalid");

	private Integer status;
	private String value;
	private String statusClassify;


	BossOrderStatus(Integer status, String value,String statusClassify) {
		this.status = status;
		this.value = value;
		this.statusClassify=statusClassify;
	}

	public String getValue() {
		return value;
	}

	public Integer getStatus() {
		return status;
	}

	public static final Map<Integer, BossOrderStatus> maps = new HashMap<Integer, BossOrderStatus>() {
		private static final long serialVersionUID = -8986866330615001847L;
		{
			for (BossOrderStatus enu : BossOrderStatus.values()) {
				put(enu.getStatus(), enu);
			}
		}
	};

	public static boolean check(Integer status, BossOrderStatus... bossStatus) {
		if (status == null || !maps.containsKey(status.intValue()) || bossStatus == null || bossStatus.length <= 0){
			return false;
		}
		for (BossOrderStatus tlStatus: bossStatus){
			if (status.intValue() == tlStatus.getStatus()){
				return true;
			}
		}
		return false;
	}
	
	public static String getValue(Integer status) {
		if (status == null || status == 0) {
			return null;
		}
		BossOrderStatus bossStatus = maps.get(status);
		return bossStatus == null ? null : bossStatus.getValue();
	}

	public String getStatusClassify() {
		return statusClassify;
	}
}
