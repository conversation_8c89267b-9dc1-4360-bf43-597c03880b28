package com.sgs.preorder.facade.model.enums;

public enum QuotationCopyType {
    CopyEnquiry(1, "CopyEnquiry"),
    CopyOrder(2, "CopyOrder");

    private int status;
    private String code;

    QuotationCopyType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return code;
    }
}
