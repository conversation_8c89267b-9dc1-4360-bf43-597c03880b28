package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ObjectTypeEnum {
    enquiry("enquiry", "Enquiry"),
    order("order", "Order"),
    report("report", "Report"),
    job("job", "Job"),
    subcontract("subcontract", "Subcontract"),
    customer("customer", "Customer");

    private String code;
    private String name;

    ObjectTypeEnum(String code,String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return this.code;
    }

    static Map<String, ObjectTypeEnum> maps = new HashMap<>();

    static {
        for (ObjectTypeEnum type : ObjectTypeEnum.values()) {
            maps.put(type.getCode(), type);
        }
    }

    public static ObjectTypeEnum findCode(String code) {
        if (code == null || !maps.containsKey(code)){
            return null;
        }
        return maps.get(code);
    }

    public static boolean check(String code) {
        if (code == null){
            return false;
        }
        return maps.containsKey(code);
    }

    /**
     *
     * @param code
     * @param amendType
     * @return
     */
    public static boolean check(String code, ObjectTypeEnum amendType) {
        if (code == null || !maps.containsKey(code)){
            return false;
        }
        return maps.get(code) == amendType;
    }
}