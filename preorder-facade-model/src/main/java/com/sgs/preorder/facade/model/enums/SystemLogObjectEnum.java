package com.sgs.preorder.facade.model.enums;

public enum SystemLogObjectEnum {

    QUOTATION("quotation","QuotationList查询","Data Search"),
    JOB("job","JobList查询","Data Search"),
    TEST_LINE("testLine","TestLineList查询","Data Search"),
    REPORT("report","ReportList查询","Data Search"),
    SUBCONTRACT("subcontract","SubcontractList查询","Data Search"),
    ORDER("order","OrderList查询","Data Search"),
    SALES_ORDER("salesOrder","SalesOrderList查询","Data Search"),
    BOSS_ORDER("bossOrder","BossOrderList查询","Data Search"),
    ENQUIRY("enquiry","EnquiryList查询","Data Search"),
    ENQUIRY_SALES("enquirySales","EnquirySalesList查询","Data Search"),
    COST_LIST("costList","CostList查询","Data Search"),
    PAYMENT_LIST("paymentList","PaymentList查询","Data Search"),
    DUE_DATE_CONFIRM("dueDateConfirm","SetDueDateConfirm","Set DueDate Confirm"),
    PAIDUP_LIST("paidUptList","paidUpList查询","Data Search");

    private String type;
    private String desc;
    private String opType;

    SystemLogObjectEnum(String type,String desc,String opType) {
        this.type = type;
        this.desc = desc;
        this.opType = opType;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }
}
