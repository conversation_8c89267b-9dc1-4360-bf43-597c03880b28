package com.sgs.preorder.facade.model.enums;

public enum ServiceItemTypeEnum {
	TEST_LINE(1, "testline"),
	PP(2, "pp"),
	FCM(3,"fcm");

	private Integer code;
	private String name;
	private ServiceItemTypeEnum(Integer code, String name){
		this.code = code;
		this.name= name;
	}
	
	public Integer getCode(){
		return this.code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public static ServiceItemTypeEnum enumOfCode(Integer code) {
		for(ServiceItemTypeEnum type: ServiceItemTypeEnum.values()) {
			if(type.getCode().equals(code)) {
				return type;
			}
		}
		return null;
	}
	public static boolean check(Integer code, ServiceItemTypeEnum... serviceItemTypeEnums){
		if(code==null){
			return false;
		}
		for(ServiceItemTypeEnum type: serviceItemTypeEnums) {
			if(type.getCode().equals(code)) {
				return true;
			}
		}
		return false;
	}
	public static ServiceItemTypeEnum enumOfName(String name) {
		for(ServiceItemTypeEnum type: ServiceItemTypeEnum.values()) {
			if(name!=null&&type.getName().equals(name)) {
				return type;
			}
		}
		return null;
	}

	public static Integer serviceItemTypeToArtifactType(Integer serviceItemType) {
		if(serviceItemType==null){
			return null;
		}
		if(serviceItemType.equals(PP.getCode())){
			return 1;
		}
		if(serviceItemType.equals(TEST_LINE.getCode())){
			return 0;
		}
		return null;
	}
}
