package com.sgs.preorder.facade.model.kafka;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * TRF提交消息体
 * <AUTHOR>
 * @date 2019/11/7 15:02
 */
@Getter
@Setter
public class TrfSubmittedMessage {
    private String trfNo;
    private Date trfSubmissionDate;
    private String buyerNameEN;
    private String applicantNameEN;
    private String payerNameEN;
    private String labContact;
    private String labCode;
    private String source;
}
