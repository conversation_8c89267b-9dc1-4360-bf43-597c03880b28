package com.sgs.preorder.facade.model.enums;

public enum ExpressContentType {
    SAMPLE((byte)1, "Sample"),
    INVOICE((byte)2,"Invoice"),
    HARDCOPY_REPORT((byte)3, "HardCopy Report");

    private byte code;
    private String name;
    private ExpressContentType(byte code, String name){
        this.code = code;
        this.name= name;
    }

    public byte getCode(){
        return this.code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static ExpressContentType enumOf(Byte code) {
        for(ExpressContentType type: ExpressContentType.values()) {
            if(type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
