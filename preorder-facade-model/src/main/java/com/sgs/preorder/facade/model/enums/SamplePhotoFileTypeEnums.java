package com.sgs.preorder.facade.model.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum SamplePhotoFileTypeEnums {
	O("O", 1,"Original Sample","Original Sample","原样"),
	B("B", 2,"Before Testing","Before Test","测试前"),
	M("M", 3,"on Middle Testing","During Test","测试中"),
	A("A", 4,"After Testing","After Test","测试后");
	private String  status;
	private Integer type;
	private String value;
	private String typeEn;
	private String typeCn;

	SamplePhotoFileTypeEnums(String status,Integer type, String value,String typeEn,String typeCn) {
		this.status = status;
		this.type = type;
		this.value = value;
		this.typeEn = typeEn;
		this.typeCn = typeCn;
	}

	public String getValue() {
		return value;
	}

	public String getStatus() {
		return status;
	}

	public Integer getType() {
		return type;
	}

	public String getTypeEn() {
		return typeEn;
	}

	public String getTypeCn() {
		return typeCn;
	}

	public static final Map<String, SamplePhotoFileTypeEnums> maps = new HashMap<String, SamplePhotoFileTypeEnums>() {
		private static final long serialVersionUID = -8986866330615001847L;
		{
			for (SamplePhotoFileTypeEnums enu : SamplePhotoFileTypeEnums.values()) {
				put(enu.getStatus(), enu);
			}
		}
	};

	public static final Map<Integer, SamplePhotoFileTypeEnums> mapsByType = new HashMap<Integer, SamplePhotoFileTypeEnums>() {
		private static final long serialVersionUID = -8986866330615001847L;
		{
			for (SamplePhotoFileTypeEnums enu : SamplePhotoFileTypeEnums.values()) {
				put(enu.getType(), enu);
			}
		}
	};

	public static boolean check(String status, SamplePhotoFileTypeEnums caseType) {
		if (StringUtils.isBlank(status)) {
			return false;
		}
		return maps.get(status) == caseType;
	}

	public static String getValue(String status) {
		if (StringUtils.isBlank(status)) {
			return null;
		}
		SamplePhotoFileTypeEnums orderStatus = maps.get(status);
		return orderStatus == null ? null : orderStatus.getValue();
	}

	public static Integer getType(String status) {
		if (StringUtils.isBlank(status)) {
			return null;
		}
		SamplePhotoFileTypeEnums orderStatus = maps.get(status);
		return orderStatus == null ? null : orderStatus.getType();
	}

	public static String getTypeEn(Integer type) {
		if (null == type) {
			return null;
		}
		SamplePhotoFileTypeEnums orderStatus = mapsByType.get(type);
		return orderStatus == null ? null : orderStatus.getTypeEn();
	}

	public static String getTypeCn(Integer type) {
		if (null == type) {
			return null;
		}
		SamplePhotoFileTypeEnums orderStatus = mapsByType.get(type);
		return orderStatus == null ? null : orderStatus.getTypeCn();
	}
}
