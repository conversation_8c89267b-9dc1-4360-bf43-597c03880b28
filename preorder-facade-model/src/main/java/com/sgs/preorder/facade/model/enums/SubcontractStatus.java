package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum SubcontractStatus {
    // 0-new,1-testing,2-complete
    New(0, "new"),
    Testing(1, "testing"),
    Complete(2, "complete"),
    Cancelled(3, "cancelled"),
    Pending(4, "Pending");

    private final int code;
    private final String message;

    SubcontractStatus(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String message() {
        return this.message;
    }

    public static final Map<Integer, SubcontractStatus> CACHE = new HashMap<Integer, SubcontractStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (SubcontractStatus enu : SubcontractStatus.values()) {
                put(enu.getCode(), enu);
            }
        }
    };

    public static SubcontractStatus getCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SubcontractStatus item : SubcontractStatus.values()) {
            if (code.equals(item.getCode())) {
                return item;
            }
        }
        return null;
    }

    public static String getMessage(Integer status) {
        if (status == null) {
            return null;
        }
        SubcontractStatus subcontractStatus = CACHE.get(status);
        return subcontractStatus == null ? null : subcontractStatus.message();
    }
}
