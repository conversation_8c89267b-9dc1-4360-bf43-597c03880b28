package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum CoverpageStatusEnums {

	Transfer(1,"Transfer"),
	Confirmed(2,"Confirmed"),
	Completed(3,"Completed");

	private Integer coverpageStatus;
	private String message;

	CoverpageStatusEnums(Integer coverpageStatus, String message) {
		this.coverpageStatus = coverpageStatus;
		this.message = message;
	}

	public Integer getCoverpageStatus() {
		return this.coverpageStatus;
	}

	public String getMessage() {
		return message;
	}

	public static final Map<Integer, CoverpageStatusEnums> maps = new HashMap<Integer, CoverpageStatusEnums>() {
		private static final long serialVersionUID = -8986866330615001847L;
		{
			for (CoverpageStatusEnums enu : CoverpageStatusEnums.values()) {
				put(enu.getCoverpageStatus(), enu);
			}
		}
	};

	public static String getMessage(Integer status) {
		if (status == null || !maps.containsKey(status.intValue())) {
			return null;
		}
		return maps.get(status).getMessage();
	}
}
