package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * ServiceLevel 对应枚举
 */
public enum
ServiceLevelEnum {
    Regular("1","Regular",""),
    Express("2","Express","加急"),
    DoubleExpress("3","Double express","特急"),
    Emergency("4","Emergency","紧急"),
    Regular_5WDS("5","Regular(5WDS)",""),
    Regular_3WDS("6","Regular(3WDS)","");
    private String level;
    private String val;
    private String desc;

    ServiceLevelEnum(String level, String val,String desc) {
        this.level = level;
        this.val = val;
        this.desc = desc;
    }

    public static final Map<String, ServiceLevelEnum> maps = new HashMap<String, ServiceLevelEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (ServiceLevelEnum serviceLevelEnum : ServiceLevelEnum.values()) {
                put(serviceLevelEnum.getLevel(), serviceLevelEnum);
            }
        }
    };

    public static String getServiceLevelValueByLevel(String level){
        for (ServiceLevelEnum serviceLevelEnum : ServiceLevelEnum.values()) {
            if(serviceLevelEnum.getLevel().equalsIgnoreCase(level)){
                return serviceLevelEnum.getVal();
            }
        }
        return "";
    }
    public static String getServiceLevelDescByLevel(String level){
        for (ServiceLevelEnum serviceLevelEnum : ServiceLevelEnum.values()) {
            if(serviceLevelEnum.getLevel().equalsIgnoreCase(level)){
                return serviceLevelEnum.getDesc();
            }
        }
        return "";
    }

    public static ServiceLevelEnum getServiceLevel(String level) {
        if (level == null || !maps.containsKey(level)) {
            return null;
        }
        return maps.get(level);
    }


    public static String getServiceLevelByLevelValue(String LevelValue){
        for (ServiceLevelEnum serviceLevelEnum : ServiceLevelEnum.values()) {
            if(serviceLevelEnum.getVal().equalsIgnoreCase(LevelValue)){
                return serviceLevelEnum.getLevel();
            }
        }
        return "";
    }

    public String getLevel() {
        return level;
    }


    public String getVal() {
        return val;
    }

    public String getDesc(){
        return desc;
    }
}
