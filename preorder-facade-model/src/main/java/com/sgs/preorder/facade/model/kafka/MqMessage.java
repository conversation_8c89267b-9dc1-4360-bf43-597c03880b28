package com.sgs.preorder.facade.model.kafka;

public class MqMessage<T> {
    private String action;
    private String actionType;
    private String sgsToken;
    private String account;
    private String productLineCode;
    private String labCode;

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    private T data;

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getSgsToken() {
        return sgsToken;
    }

    public void setSgsToken(String sgsToken) {
        this.sgsToken = sgsToken;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getProductLineCode() {
        return productLineCode;
    }

    public void setProductLineCode(String productLineCode) {
        this.productLineCode = productLineCode;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }
}
