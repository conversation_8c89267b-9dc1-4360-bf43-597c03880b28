package com.sgs.preorder.facade.model.ordercopy;

import com.sgs.framework.core.base.BaseProductLine;

public class TestMatrixInfo extends BaseProductLine {
    /**
     *
     */
    private String groupId;
    /**
     *
     */
    private String testMatrixId;
    /**
     *
     */
    private String testSampleId;
    /**
     *
     */
    private String testLineInstanceId;

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getTestMatrixId() {
        return testMatrixId;
    }

    public void setTestMatrixId(String testMatrixId) {
        this.testMatrixId = testMatrixId;
    }

    public String getTestSampleId() {
        return testSampleId;
    }

    public void setTestSampleId(String testSampleId) {
        this.testSampleId = testSampleId;
    }

    public String getTestLineInstanceId() {
        return testLineInstanceId;
    }

    public void setTestLineInstanceId(String testLineInstanceId) {
        this.testLineInstanceId = testLineInstanceId;
    }
}
