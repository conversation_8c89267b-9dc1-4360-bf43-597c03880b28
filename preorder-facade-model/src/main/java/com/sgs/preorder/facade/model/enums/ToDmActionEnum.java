package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 定义了TODM时的类型，不同的类型传递的数据不同。
 * <AUTHOR>
 * @date 2020/11/25 11:04
 */
public enum ToDmActionEnum {
    ReportApprove(1,"ReportApprove"), ReportRework(2,"ReportRework"), ConfirmMatrix(3, "ConfirmMatrix"),
    OrderCreated(4, "OrderCreated"),
    OrderCompleted(5, "OrderCompleted"), OrderCancel(6, "OrderCancel");


    ToDmActionEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static final Map<Integer, ToDmActionEnum> maps = new HashMap<>();

    static {
        for (ToDmActionEnum enu : ToDmActionEnum.values()) {
            maps.put(enu.getCode(), enu);
        }
    }

    public static boolean check(Integer code, ToDmActionEnum...actionEnums){
        if (code == null || !maps.containsKey(code) || actionEnums == null || actionEnums.length <= 0) {
            return false;
        }
        for (ToDmActionEnum actionEnum: actionEnums){
            if (code.intValue() == actionEnum.getCode()){
                return true;
            }
        }
        return false;
    }

    public static ToDmActionEnum getInstance(Integer code){
        return Optional.ofNullable(maps.get(code)).orElse(null);
    }
}
