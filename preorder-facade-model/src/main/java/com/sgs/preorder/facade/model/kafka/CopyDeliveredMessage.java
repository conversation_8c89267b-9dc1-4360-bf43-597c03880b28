package com.sgs.preorder.facade.model.kafka;

import com.sgs.framework.core.base.BaseProductLine;

public class CopyDeliveredMessage extends BaseProductLine {
    private String orderNo;
    //private String deliveryRemark;
    private String softCopyDeliveryDate;
    private String hardCopyDeliveryDate;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSoftCopyDeliveryDate() {
        return softCopyDeliveryDate;
    }

    public void setSoftCopyDeliveryDate(String softCopyDeliveryDate) {
        this.softCopyDeliveryDate = softCopyDeliveryDate;
    }

    public String getHardCopyDeliveryDate() {
        return hardCopyDeliveryDate;
    }

    public void setHardCopyDeliveryDate(String hardCopyDeliveryDate) {
        this.hardCopyDeliveryDate = hardCopyDeliveryDate;
    }
}
