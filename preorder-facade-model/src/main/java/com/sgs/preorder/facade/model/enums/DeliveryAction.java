package com.sgs.preorder.facade.model.enums;

public enum DeliveryAction {
    SendSoftcopyEmail("10", "SendSoftcopyEmail"),
    ConfirmEmail("20", "ConfirmEmail"),
    DeliverySoftCopy("30", "DeliverySoftCopy"),
    SendPlayLoadToCp("40", "SendPlayLoadToCp"),
    SendDraftEmail("50", "SendDraftEmial"),
    SubReportDelivery("60", "SubReportDelivery")
    ;
    private String status;
    private String value;

    DeliveryAction(String status, String value) {
        this.status = status;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public String getStatus() {
        return status;
    }
}
