package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum OperationModeEnum {
    FullCycle((1 << 0), "Full Cycle"),
    TestingOnly((1 << 1), "Testing Only"),
    TracingOnly((1 << 2), "Tracing Only"),
    ChargeOnly((1 << 3), "Charge Only");

    private final int operationMode;
    private final String message;

    OperationModeEnum(int operationMode, String message) {
        this.operationMode = operationMode;
        this.message = message;
    }

    public int getOperationMode() {
        return operationMode;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, OperationModeEnum> maps = new HashMap<Integer, OperationModeEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (OperationModeEnum enu : OperationModeEnum.values()) {
                put(enu.getOperationMode(), enu);
            }
        }
    };

    public static OperationModeEnum findMode(Integer operationMode, OperationModeEnum defMode) {
        if (operationMode == null || !maps.containsKey(operationMode.intValue())) {
            return defMode;
        }
        return maps.get(operationMode.intValue());
    }

    public static OperationModeEnum findMode(Integer operationMode) {
        if (operationMode == null || !maps.containsKey(operationMode.intValue())) {
            return null;
        }
        return maps.get(operationMode.intValue());
    }

    /**
     *
     * @param operationMode
     * @param type
     * @return
     */
    public static boolean check(Integer operationMode, OperationModeEnum type) {
        if (operationMode == null || !maps.containsKey(operationMode.intValue())){
            return false;
        }
        return maps.get(operationMode.intValue()) == type;
    }

    public boolean check(OperationModeEnum... operationModes){
        if (operationModes == null || operationModes.length <= 0){
            return false;
        }
        for (OperationModeEnum operationMode: operationModes){
            if (this.getOperationMode() == operationMode.getOperationMode()){
                return true;
            }
        }
        return false;
    }

    public static boolean check(Integer operationMode,OperationModeEnum... operationModeEnums){
        if(operationMode==null){
            return false;
        }
        for(OperationModeEnum item: operationModeEnums) {
            if(item.getOperationMode() == operationMode) {
                return true;
            }
        }
        return false;
    }

}