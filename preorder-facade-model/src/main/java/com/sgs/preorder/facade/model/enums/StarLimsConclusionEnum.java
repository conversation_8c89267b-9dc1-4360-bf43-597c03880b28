package com.sgs.preorder.facade.model.enums;


/**
 * ServiceLevel 对应枚举
 */
public enum StarLimsConclusionEnum {
    PASS("5","Pass"),
    FAIL("1","FAIL"),
    NA("3","N/A"),
    NT("13","N/T"),//todo
    DATA("14","DATA"),
    SEERESULT("12","See Result");//todo
    private String level;
    private String val;

    StarLimsConclusionEnum(String level, String val) {
        this.level = level;
        this.val = val;
    }


    public String getLevel() {
        return level;
    }


    public String getVal() {
        return val;
    }
}
