package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum AmendReportType {
    AmendByOrder(0, "Amend By Order"),
    AmendByReport(1, "Amend By Report"),
    ;
    private final int type;
    private final String message;

    AmendReportType(int type, String message) {
        this.type = type;
        this.message = message;
    }

    public int getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, AmendReportType> maps = new HashMap<Integer, AmendReportType>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (AmendReportType enu : AmendReportType.values()) {
                put(enu.getType(), enu);
            }
        }
    };

    public static AmendReportType findType(Integer status) {
        //jira 11232
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    /**
     * @param status
     * @param type
     * @return
     */
    public static boolean check(Integer status, AmendReportType type) {
        //jira 11232
        if (status == null || !maps.containsKey(status)) {
            return false;
        }
        return maps.get(status) == type;
    }

    /**
     *
     * @param amendType
     * @return
     */
    public boolean check(AmendReportType amendType) {
        if (amendType == null){
            return false;
        }
        return this == amendType;
    }

    public boolean check(AmendReportType... types) {
        if (types == null || types.length <= 0) {
            return false;
        }
        for (AmendReportType type : types) {
            if (this.getType() == type.getType()) {
                return true;
            }
        }
        return false;
    }
}
