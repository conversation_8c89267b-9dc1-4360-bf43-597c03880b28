package com.sgs.preorder.facade.model.enums;

/**
 * @EnmuName : CopyCacheKeyEnums
 * @Description :copy时，放到redis中的key的前缀，后缀默认是新单id
 * @Authoer : vincent.zhi
 * @Date : 2019/8/16 10:49
 **/
public enum CopyCacheKeyEnums {
	sgsToken("sgsToken_"),
	copyType("copyType_"),
	copyAction("copyAction_"),
	conclusionSummaryVO("conclusionSummaryVO_"),
	oldNewSampleID("oldNewSampleID_");

	private String key;
	CopyCacheKeyEnums(String key){
		this.key = key;
	}

	public String getKey() {
		return key;
	}
}
