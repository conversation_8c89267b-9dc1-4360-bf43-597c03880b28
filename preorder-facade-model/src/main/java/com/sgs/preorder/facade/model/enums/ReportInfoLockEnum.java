package com.sgs.preorder.facade.model.enums;

public enum ReportInfoLockEnum {

    LOCK(1, "LOCK"),
    UN_LOCK(0, "UN_LOCK");

    private Integer status;
    private String value;

    ReportInfoLockEnum(Integer status, String value) {
        this.status = status;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public Integer getStatus() {
        return status;
    }
}
