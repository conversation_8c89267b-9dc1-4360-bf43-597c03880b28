package com.sgs.preorder.facade.model.enums;

/**
 * <AUTHOR>
 */

public enum TrfSourceType {
    Order2TRF("Order2TRF", "订单转TRF"),
    TRF2Order("TRF2Order", "TRF转Order");

    private String sourceType;
    private String description;

    TrfSourceType(String sourceType, String description) {
        this.sourceType = sourceType;
        this.description = description;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
