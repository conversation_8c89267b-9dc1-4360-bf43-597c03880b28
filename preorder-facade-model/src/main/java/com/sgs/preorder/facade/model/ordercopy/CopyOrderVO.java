package com.sgs.preorder.facade.model.ordercopy;

import java.io.Serializable;

public class CopyOrderVO implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String sgsToken;
	private String copyType;
	private String orderNo;
	private String targetOrderNo;
	private String subContractNo;
	private String postfix;
	private String type;

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getPostfix() {
		return postfix;
	}

	public void setPostfix(String postfix) {
		this.postfix = postfix;
	}

	public String getSgsToken() {
		return sgsToken;
	}

	public void setSgsToken(String sgsToken) {
		this.sgsToken = sgsToken;
	}

	public String getCopyType() {
		return copyType;
	}

	public void setCopyType(String copyType) {
		this.copyType = copyType;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getTargetOrderNo() {
		return targetOrderNo;
	}

	public void setTargetOrderNo(String targetOrderNo) {
		this.targetOrderNo = targetOrderNo;
	}

	public String getSubContractNo() {
		return subContractNo;
	}

	public void setSubContractNo(String subContractNo) {
		this.subContractNo = subContractNo;
	}

}
