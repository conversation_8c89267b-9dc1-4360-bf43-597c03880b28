package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

import static com.sgs.preorder.facade.model.enums.OrderCopyType.*;

public enum SubcontractMode {
    Host(1, SubContract, "Host"),
    Exec(2, NewSubContract, "Exec"),
    Light(3, LightSubContract, "Light");

    private final int code;
    private final OrderCopyType subCopyType;
    private final String message;

    SubcontractMode(int code, OrderCopyType subCopyType, String message) {
        this.code = code;
        this.subCopyType = subCopyType;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public OrderCopyType getSubCopyType() {
        return subCopyType;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, SubcontractMode> CACHE = new HashMap<Integer, SubcontractMode>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (SubcontractMode enu : SubcontractMode.values()) {
                put(enu.getCode(), enu);
            }
        }
    };

    public static SubcontractMode getCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SubcontractMode item : SubcontractMode.values()) {
            if (code.equals(item.getCode())) {
                return item;
            }
        }
        return null;
    }

    public static SubcontractMode findSubContractMode(Integer status) {
        if (status == null || !CACHE.containsKey(status.intValue())) {
            return null;
        }
        return CACHE.get(status);
    }

    public static String getMessage(Integer status) {
        if (status == null) {
            return null;
        }
        SubcontractMode item = CACHE.get(status);
        return item == null ? null : item.getMessage();
    }

    public static OrderCopyType getSubCopyType(Integer status) {
        if (status == null) {
            return null;
        }
        SubcontractMode item = CACHE.get(status);
        return item == null ? null : item.getSubCopyType();
    }

    /**
     *
     * @param status
     * @param defaultSubCopyType 默认值
     * @return
     */
    public static OrderCopyType getSubCopyTypeDefault(Integer status, OrderCopyType defaultSubCopyType) {
        if (status == null) {
            return defaultSubCopyType;
        }
        SubcontractMode item = CACHE.get(status);
        return item == null ? defaultSubCopyType : item.getSubCopyType();
    }

}
