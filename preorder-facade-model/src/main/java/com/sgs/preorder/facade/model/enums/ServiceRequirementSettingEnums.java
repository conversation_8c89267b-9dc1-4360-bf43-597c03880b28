package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ServiceRequirementSettingEnums {
    returnSampleTo("returnSampleTo", "Return Sample To"),
    invoiceDeliverWay("invoiceDeliverWay", "Invoice Deliver Way"),
    hardCopyReportDeliverWay("hardCopyReportDeliverWay", "Hardcopy Report Deliver Way"),
    qrCodeFlag("qrCodeFlag", "QR Code"),
    sealFlag("sealFlag", "Seal"),
    needConclusion("needConclusion", "Need Conclusion"),
    photoRequest("photoRequest", "Photo Request"),
    reportRequirement("reportRequirement", "Report Requirement"),
    softCopyDeliverTo("softCopyDeliverTo", "SoftCopy Deliver To"),
    hardCopyDeliverTo("hardCopyDeliverTo", "HardCopy Deliver To"),
    qualificationType("qualificationType", "Qualification Type"),
    isDraftReportRequired("isDraftReportRequired", "Draft Report Required"),
    liquidTestSample("liquidTestSample", "For liquid,printing ink sample"),
    vatType("vatType", "VAT Type"),
    softCopyDeliverCC("softCopyDeliverCC", "SoftCopy Deliver CC"),
    invoiceDeliverTo("invoiceDeliverTo", "Invoice Deliver To"),
    prelimReport("prelimReport", "Prelim Report"),
    prelimReportCC("prelimReportCC", "Prelim Report CC"),
    returnSampleWay("returnSampleWay", "Return Sample Way"),
    commentFlag("commentFlag", "Conclusion Required"),
    hardCopyFlag("hardCopyFlag", "Hardcopy Required"),
    takePhotoFlag("takePhotoFlag", "Photo Required"),
    confirmCoverPageFlag("confirmCoverPageFlag", "Cover Page Confirmation Required"),
    takePhotoRemark("takePhotoRemark", "Photo Taking Remarks"),
    reportLanguage("reportLanguage", "Report Language"),
    paymentRemark("paymentRemark", "Payment Remark")
    ;
    private String code;
    private String name;

    ServiceRequirementSettingEnums(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
    public static final Map<String, ServiceRequirementSettingEnums> maps = new HashMap<String, ServiceRequirementSettingEnums>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ServiceRequirementSettingEnums type : ServiceRequirementSettingEnums.values()) {
                put(type.getCode(), type);
            }
        }
    };

    public static ServiceRequirementSettingEnums getType(String code) {
        if (code == null || !maps.containsKey(code)) {
            return null;
        }
        return maps.get(code);
    }
}
