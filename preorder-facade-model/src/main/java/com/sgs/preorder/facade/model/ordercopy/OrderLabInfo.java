package com.sgs.preorder.facade.model.ordercopy;

import java.util.Date;

public class OrderLabInfo {
    /**
     *
     */
    private String orderId;
    /**
     *
     */
    private Integer orderStatus;
    /**
     *
     */
    private String locationCode;
    /**
     *
     */
    private Integer labId;
    /**
     *
     */
    private String labCode;
    /**
     *
     */
    private String postfix;
    /**
     *
     */
    private Integer orderCategory;
    /**
     *
     */
    private String oldOrderNo;
    /**
     *
     */
    private Integer serviceLevel;
    /**
     *
     */
    private Date createdDate;
    /**
     *
     */
    private Integer operationType;
    /**
     *
     */
    private Integer toTestFlag;
    /**
     *
     */
    private Integer operationMode;
    /**
     *
     */
    private String operationModeName;
    /**
     *
     */
    private Integer buId;

    public Integer getOperationMode() {
        return operationMode;
    }

    public void setOperationMode(Integer operationMode) {
        this.operationMode = operationMode;
    }

    public String getOperationModeName() {
        return operationModeName;
    }

    public void setOperationModeName(String operationModeName) {
        this.operationModeName = operationModeName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public Integer getLabId() {
        return labId;
    }

    public void setLabId(Integer labId) {
        this.labId = labId;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getPostfix() {
        return postfix;
    }

    public void setPostfix(String postfix) {
        this.postfix = postfix;
    }

    public Integer getOrderCategory() {
        return orderCategory;
    }

    public void setOrderCategory(Integer orderCategory) {
        this.orderCategory = orderCategory;
    }

    public String getOldOrderNo() {
        return oldOrderNo;
    }

    public void setOldOrderNo(String oldOrderNo) {
        this.oldOrderNo = oldOrderNo;
    }

    public Integer getServiceLevel() {
        return serviceLevel;
    }

    public void setServiceLevel(Integer serviceLevel) {
        this.serviceLevel = serviceLevel;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public Integer getToTestFlag() {
        return toTestFlag;
    }

    public void setToTestFlag(Integer toTestFlag) {
        this.toTestFlag = toTestFlag;
    }

    public Integer getBuId() {
        return buId;
    }

    public void setBuId(Integer buId) {
        this.buId = buId;
    }
}
