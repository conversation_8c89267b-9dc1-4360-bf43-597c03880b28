package com.sgs.preorder.facade.model.ordercopy;

import com.sgs.preorder.facade.model.dto.customer.ContactAddressDTO;
import com.sgs.preorder.facade.model.enums.OrderCopyType;
import com.sgs.preorder.facade.model.info.ReportInfo;
import com.sgs.preorder.facade.model.info.user.UserLabBuInfo;
import com.sgs.preorder.facade.model.po.SubcontractRelInfoPO;

import java.util.*;

public class SysCopyInfo {
    /**
     *
     */
    public SysCopyInfo(){
        oldRelIds = new HashMap();
        needUpdateSubRelMap = new HashMap<>();
        reportIdMap = new HashMap<>();
        reportNoMap = new HashMap<>();
        oldTestLineInstanceIds = new HashMap<>();
        oldTestMatrixIds = new HashMap();
        oldReportMatrixIds = new HashMap();
    }

    /**
     *
     */
    private OrderCopyType copyType;
    /**
     *
     */
    private String orderId;
    /**
     *
     */
    private String orderNo;

    private String externalOrderNo;
    private String rootExternalOrderNo;
    /**
     *
     */
    private String oldOrderId;
    /**
     *
     */
    private String oldOrderNo;
    /**
     *
     */
    private String subContractNo;
    /**
     *
     */
    private String reportId;
    private Integer reportFlag;
    /**
     *
     */
    private String reportNo;
    private String externalReportNo;
    /**
     *
     */
    private String oldReportId;
    /**
     *
     */
    private String oldReportNo;

    private List<String> oldReportNoList;
    /**
     * 用于保存Split 时 需要更新的reportNo
     */
    private String oldReportNoUpdate;
    /**
     * CopyOrderService.newByCopyInfo 方法会计算该值
     */
    private CopySlOrderInfo slOrder;
    /**
     *
     */
    private UserLabBuInfo labBu;
    /**
     *
     */
    private CustomerBuyerCopyInfo customerBuyer;
    /**
     *
     */
    private Date reportExpectDueDate;
    private Date jobDueDate;
    private Date subcontractDueDate;
    /**
     * SlOrderCopyService 使用
     */
    private Integer serviceLevel;
    /**
     * SlOrderCopyService 使用
     */
    private String oldLab;
    /**
     * AmendReport 默认按Order处理
     * 0:按Order 处理
     * 1:按Report处理
     */
    private int amendReportType;
    /**
     * 需要在Amend同时 创建的内部分包 Id
     */
    private List<String> subContractIds;
    /**
     *
     */
    private Map<String, String> oldProductIds;
    /**
     *
     */
    private Map<String, String> needUpdateSubRelMap;
    /**
     *
     */
    private Set<OriginalSampleCopyInfo> originalSamples;
    /**
     * 在TestRequestCopyService使用   Customser
     */
    private ContactAddressDTO trfCustomer;
    /**
     *
     */
    private boolean isSyncOtsNotes;
    /**
     *
     */
    private String postfix;
    private String[] productCategories;
    /**
     * OriginalRelId，
     */
    private Map<String, SubcontractRelInfoPO> oldRelIds;
    /**
     *
     */
    private Set<String> oldMatrixIds;
    /**
     *
     */
    private Map<String, String> originalSampleIds;
    private Map<String,String> reportIdMap;
    private Map<String,String> reportNoMap;

    private Map<String,String> oldTestLineInstanceIds;
    private Map<String,String> oldTestMatrixIds;
    private Map<String,String> oldReportMatrixIds;

    /**
     * 拆分时 用到 用来给新的reportNo做序号用的
     */
    private int reportNoSeq;

    /**
     *
     */
    private Integer transToLang;
    /**
     *
     */
    private List<CopyReportInfo> testMatrixs;


    private String newOrderQrcodeFlag;

    /**
     * object setting 配置
     */
    private Map<String,String> displayFields;

    private String regionAccount;

    private String subContractFromLab;

    private Date newReportExpectDueDate;

    private Integer paymentStatus;
    private String enquiryId;
    private String enquiryNo;
    private String token;
    private Boolean convertOriginalSample;

    private String caseType;

    /**
     * 分包单上的ExtData字段
     */
    private String extData;

    private List<ReportInfo> oldReportList;

    private boolean extendsReportMatrix = false;


    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getEnquiryId() {
        return enquiryId;
    }

    public void setEnquiryId(String enquiryId) {
        this.enquiryId = enquiryId;
    }

    public String getEnquiryNo() {
        return enquiryNo;
    }

    public void setEnquiryNo(String enquiryNo) {
        this.enquiryNo = enquiryNo;
    }

    public Integer getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(Integer paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public Date getNewReportExpectDueDate() {
        return newReportExpectDueDate;
    }

    public void setNewReportExpectDueDate(Date newReportExpectDueDate) {
        this.newReportExpectDueDate = newReportExpectDueDate;
    }

    public String getRegionAccount() {
        return regionAccount;
    }

    public void setRegionAccount(String regionAccount) {
        this.regionAccount = regionAccount;
    }

    public Map<String, String> getDisplayFields() {
        return displayFields;
    }

    public void setDisplayFields(Map<String, String> displayFields) {
        this.displayFields = displayFields;
    }

    public String getNewOrderQrcodeFlag() {
        return newOrderQrcodeFlag;
    }

    public void setNewOrderQrcodeFlag(String newOrderQrcodeFlag) {
        this.newOrderQrcodeFlag = newOrderQrcodeFlag;
    }

    public OrderCopyType getCopyType() {
        return copyType;
    }

    public void setCopyType(OrderCopyType copyType) {
        this.copyType = copyType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOldOrderId() {
        return oldOrderId;
    }

    public void setOldOrderId(String oldOrderId) {
        this.oldOrderId = oldOrderId;
    }

    public String getOldOrderNo() {
        return oldOrderNo;
    }

    public void setOldOrderNo(String oldOrderNo) {
        this.oldOrderNo = oldOrderNo;
    }

    public String getOldReportId() {
        return oldReportId;
    }

    public void setOldReportId(String oldReportId) {
        this.oldReportId = oldReportId;
    }

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getOldReportNo() {
        return oldReportNo;
    }

    public void setOldReportNo(String oldReportNo) {
        this.oldReportNo = oldReportNo;
    }

    public String getOldReportNoUpdate() {
        return oldReportNoUpdate;
    }

    public void setOldReportNoUpdate(String oldReportNoUpdate) {
        this.oldReportNoUpdate = oldReportNoUpdate;
    }

    public CopySlOrderInfo getSlOrder() {
        return slOrder;
    }

    public void setSlOrder(CopySlOrderInfo slOrder) {
        this.slOrder = slOrder;
    }

    public UserLabBuInfo getLabBu() {
        return labBu;
    }

    public void setLabBu(UserLabBuInfo labBu) {
        this.labBu = labBu;
    }

    public CustomerBuyerCopyInfo getCustomerBuyer() {
        return customerBuyer;
    }

    public void setCustomerBuyer(CustomerBuyerCopyInfo customerBuyer) {
        this.customerBuyer = customerBuyer;
    }

    public ContactAddressDTO getTrfCustomer() {
        return trfCustomer;
    }

    public void setTrfCustomer(ContactAddressDTO trfCustomer) {
        this.trfCustomer = trfCustomer;
    }

    public Date getReportExpectDueDate() {
        return reportExpectDueDate;
    }

    public void setReportExpectDueDate(Date reportExpectDueDate) {
        this.reportExpectDueDate = reportExpectDueDate;
    }

    public Integer getServiceLevel() {
        return serviceLevel;
    }

    public void setServiceLevel(Integer serviceLevel) {
        this.serviceLevel = serviceLevel;
    }

    public String getOldLab() {
        return oldLab;
    }

    public void setOldLab(String oldLab) {
        this.oldLab = oldLab;
    }

    public int getAmendReportType() {
        return amendReportType;
    }

    public void setAmendReportType(int amendReportType) {
        this.amendReportType = amendReportType;
    }

    public List<String> getSubContractIds() {
        return subContractIds;
    }

    public void setSubContractIds(List<String> subContractIds) {
        this.subContractIds = subContractIds;
    }

    public Map<String, String> getOldProductIds() {
        return oldProductIds;
    }

    public void setOldProductIds(Map<String, String> oldProductIds) {
        this.oldProductIds = oldProductIds;
    }

    public Map<String, String> getNeedUpdateSubRelMap() {
        return needUpdateSubRelMap;
    }

    public void setNeedUpdateSubRelMap(Map<String, String> needUpdateSubRelMap) {
        this.needUpdateSubRelMap = needUpdateSubRelMap;
    }

    public Set<OriginalSampleCopyInfo> getOriginalSamples() {
        return originalSamples;
    }

    public void setOriginalSamples(Set<OriginalSampleCopyInfo> originalSamples) {
        this.originalSamples = originalSamples;
    }

    public boolean isSyncOtsNotes() {
        return isSyncOtsNotes;
    }

    public void setSyncOtsNotes(boolean syncOtsNotes) {
        isSyncOtsNotes = syncOtsNotes;
    }

    public String getSubContractNo() {
        return subContractNo;
    }

    public void setSubContractNo(String subContractNo) {
        this.subContractNo = subContractNo;
    }

    public String getPostfix() {
        return postfix;
    }

    public void setPostfix(String postfix) {
        this.postfix = postfix;
    }

    public Map<String, SubcontractRelInfoPO> getOldRelIds() {
        return oldRelIds;
    }

    public void setOldRelIds(Map<String, SubcontractRelInfoPO> oldRelIds) {
        this.oldRelIds = oldRelIds;
    }

    public int getReportNoSeq() { return reportNoSeq; }

    public void setReportNoSeq(int reportNoSeq) {   this.reportNoSeq = reportNoSeq; }

    public Set<String> getOldMatrixIds() {
        return oldMatrixIds;
    }

    public void setOldMatrixIds(Set<String> oldMatrixIds) {
        this.oldMatrixIds = oldMatrixIds;
    }

    public Map<String, String> getOriginalSampleIds() {
        return originalSampleIds;
    }

    public void setOriginalSampleIds(Map<String, String> originalSampleIds) {
        this.originalSampleIds = originalSampleIds;
    }

    public List<CopyReportInfo> getTestMatrixs() {
        return testMatrixs;
    }

    public void setTestMatrixs(List<CopyReportInfo> testMatrixs) {
        this.testMatrixs = testMatrixs;
    }

    public String[] getProductCategories() {
        return productCategories;
    }

    public void setProductCategories(String[] productCategories) {
        this.productCategories = productCategories;
    }
    public Integer getTransToLang() {
        return transToLang;
    }

    public void setTransToLang(Integer transToLang) {
        this.transToLang = transToLang;
    }

    public String getExternalOrderNo() {
        return externalOrderNo;
    }

    public void setExternalOrderNo(String externalOrderNo) {
        this.externalOrderNo = externalOrderNo;
    }

    public String getRootExternalOrderNo() {
        return rootExternalOrderNo;
    }

    public void setRootExternalOrderNo(String rootExternalOrderNo) {
        this.rootExternalOrderNo = rootExternalOrderNo;
    }

    public String getExternalReportNo() {
        return externalReportNo;
    }

    public void setExternalReportNo(String externalReportNo) {
        this.externalReportNo = externalReportNo;
    }

    public String getSubContractFromLab() {
        return subContractFromLab;
    }

    public void setSubContractFromLab(String subContractFromLab) {
        this.subContractFromLab = subContractFromLab;
    }

    public Integer getReportFlag() {
        return reportFlag;
    }

    public void setReportFlag(Integer reportFlag) {
        this.reportFlag = reportFlag;
    }

    public Boolean getConvertOriginalSample() {
        return convertOriginalSample;
    }

    public void setConvertOriginalSample(Boolean convertOriginalSample) {
        this.convertOriginalSample = convertOriginalSample;
    }

    public Map<String, String> getReportIdMap() {
        return reportIdMap;
    }

    public void setReportIdMap(Map<String, String> reportIdMap) {
        this.reportIdMap = reportIdMap;
    }

    public Map<String, String> getReportNoMap() {
        return reportNoMap;
    }

    public void setReportNoMap(Map<String, String> reportNoMap) {
        this.reportNoMap = reportNoMap;
    }

    public Date getJobDueDate() {
        return jobDueDate;
    }

    public void setJobDueDate(Date jobDueDate) {
        this.jobDueDate = jobDueDate;
    }

    public Date getSubcontractDueDate() {
        return subcontractDueDate;
    }

    public void setSubcontractDueDate(Date subcontractDueDate) {
        this.subcontractDueDate = subcontractDueDate;
    }

    public String getCaseType() {
        return caseType;
    }

    public void setCaseType(String caseType) {
        this.caseType = caseType;
    }

    public boolean isExtendsReportMatrix() {
        return extendsReportMatrix;
    }

    public void setExtendsReportMatrix(boolean extendsReportMatrix) {
        this.extendsReportMatrix = extendsReportMatrix;
    }

    public Map<String, String> getOldReportMatrixIds() {
        return oldReportMatrixIds;
    }

    public void setOldReportMatrixIds(Map<String, String> oldReportMatrixIds) {
        this.oldReportMatrixIds = oldReportMatrixIds;
    }

    public Map<String, String> getOldTestMatrixIds() {
        return oldTestMatrixIds;
    }

    public void setOldTestMatrixIds(Map<String, String> oldTestMatrixIds) {
        this.oldTestMatrixIds = oldTestMatrixIds;
    }

    public Map<String, String> getOldTestLineInstanceIds() {
        return oldTestLineInstanceIds;
    }

    public void setOldTestLineInstanceIds(Map<String, String> oldTestLineInstanceIds) {
        this.oldTestLineInstanceIds = oldTestLineInstanceIds;
    }

    public List<String> getOldReportNoList() {
        return oldReportNoList;
    }

    public void setOldReportNoList(List<String> oldReportNoList) {
        this.oldReportNoList = oldReportNoList;
    }

    public List<ReportInfo> getOldReportList() {
        return oldReportList;
    }

    public void setOldReportList(List<ReportInfo> oldReportList) {
        this.oldReportList = oldReportList;
    }
}
