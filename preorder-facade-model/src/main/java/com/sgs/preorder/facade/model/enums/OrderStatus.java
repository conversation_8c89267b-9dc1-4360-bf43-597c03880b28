package com.sgs.preorder.facade.model.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum OrderStatus {

    New(1, (1 << 0),"New"){
        @Override
        public int getRuleId() {
            return OrderStatus.compute(Confirmed, Pending, Cancelled, Testing, Completed);
        }
    },
    Quoted(2,(1 << 1), "Quoted"),
    Confirmed(3,(1 << 2), "Confirmed"){
        @Override
        public int getRuleId() {
            return OrderStatus.compute(Pending, Cancelled, Testing, Reporting, Completed);
        }
    },
    Invoiced(4,(1 << 3), "Invoiced"),
    Closed(5,(1 << 4), "Closed"){
        @Override
        public int getRuleId() {
            return OrderStatus.compute(Pending, Cancelled, Reporting);
        }
    },
    Pending(6,(1 << 5), "Pending"){
        @Override
        public int getRuleId() {
            return OrderStatus.compute(New, Confirmed, Closed, Testing, Reporting, Completed);
        }
    },
    Cancelled(7,(1 << 6), "Cancelled"),
    Testing(8,(1 << 7), "Testing"){
        @Override
        public int getRuleId() {
            return OrderStatus.compute(Pending, Cancelled, Reporting, Completed);
        }
    },
    Reporting(9,(1 << 8), "Reporting"){
        @Override
        public int getRuleId() {
            return OrderStatus.compute(Pending, Cancelled, Completed);
        }
    },
	Completed(10,(1 << 9),"Completed"){
        @Override
        public int getRuleId() {
            return OrderStatus.compute(Closed, Pending, Cancelled);
        }
    },
    QuotationConfirmed(11,(1 << 10),"QuotationConfirmed"){
        @Override
        public int getRuleId() {
            return OrderStatus.compute(Closed, Pending, Cancelled);
        }
    }
    ;

    private int status;
    private int code;
    private String message;

    OrderStatus(int status, int code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public int getStatus() {
        return status;
    }

    public int getCode() {
        return code;
    }

    public int getRuleId(){
        return 0;
    }

    public static final Map<Integer, OrderStatus> maps = new HashMap<Integer, OrderStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (OrderStatus enu : OrderStatus.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static OrderStatus getOrderStatus(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }

    /**
     *
     * @param status
     * @return
     */
    private static int compute(OrderStatus... status) {
        if (status == null || status.length <= 0) {
            return 0;
        }
        int statusVal = 0;
        for (OrderStatus statu: status){
            statusVal |= statu.getCode();
        }
        return statusVal;
    }

    /**
     *
     * @param oldStatus
     * @param newStatus
     * @return
     */
    public static boolean checkRuleStatus(OrderStatus oldStatus, OrderStatus newStatus) {
        if (oldStatus == null || newStatus == null) {
            return false;
        }
        // 只要有一个通过，就是通过
        return ((oldStatus.getRuleId() & newStatus.getCode()) > 0) || checkBackStatus(oldStatus, newStatus);
    }

    /**
     * 校验回退流程合法性
     * @param oldStatus
     * @param newStatus
     * @return
     */
    public static boolean checkBackStatus(OrderStatus oldStatus, OrderStatus newStatus){
        if (oldStatus == null || newStatus == null) {
            return false;
        }
        switch (oldStatus){
            case Closed:
                return newStatus == OrderStatus.Completed;
            case Reporting:
                return newStatus == OrderStatus.Testing;
            case Completed:
                return newStatus == OrderStatus.Reporting;
        }
        return false;
    }

    /**
     *
     * @param status
     * @param orderStatus
     * @return
     */
    public static boolean checkStatus(Integer status, OrderStatus orderStatus){
        if (status == null || !maps.containsKey(status.intValue())) {
            return false;
        }
        return maps.get(status) == orderStatus;
    }

    public static boolean checkStatus(Integer status, OrderStatus...orderStatus){
        if (status == null || !maps.containsKey(status.intValue()) || orderStatus == null || orderStatus.length <= 0) {
            return false;
        }
        for (OrderStatus orderStatu: orderStatus){
            if (status.intValue() == orderStatu.getStatus()){
                return true;
            }
        }
        return false;
    }

    public static List<Integer> availableStatusList(){
        return Arrays.asList(
                New.status,
                Quoted.status,
                Invoiced.status,
                Confirmed.status,
                Closed.status,
                Pending.status,
                Testing.status,
                Reporting.status,
                Completed.status);
    }
}