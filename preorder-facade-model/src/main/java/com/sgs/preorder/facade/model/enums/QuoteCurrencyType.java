package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/*<option value='CNY'>CNY</option>
<option value='JPY'>JPY</option>
<option value='EUR'>EUR</option>
<option value='HKD'>HKD</option>
<option value='USD'>USD</option>
<option value='AFN'>AFN</option>*/
public enum QuoteCurrencyType {
    HKD(1, "HKD"),
    CNY(2, "CNY");

    private final int type;
    private final String code;

    QuoteCurrencyType(int type, String code) {
        this.type = type;
        this.code = code;
    }

    public int getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public static final Map<Integer, QuoteCurrencyType> maps = new HashMap<Integer, QuoteCurrencyType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (QuoteCurrencyType type : QuoteCurrencyType.values()) {
                put(type.getType(), type);
            }
        }
    };

    public static QuoteCurrencyType getType(Integer type) {
        //jira-11232
        if (type == null || !maps.containsKey(type)) {
            return null;
        }
        return maps.get(type);
    }
}