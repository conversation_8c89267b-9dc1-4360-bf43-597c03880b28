package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum KafkaActionType {
    orderCreation(1, "orderCreation"),
    quotationConfirm(2, "quotationConfirm"),
    orderPending(3, "orderPending"),

    labIn(4,"labIn"),
    labOut(5,"labOut"),
    subcontract(6,"subContract"),
    paperConsolidation(7,"paperConsolidation"),
    parcelReceived(8,"parcelReceived"),
    reportApprove(9,"reportApprove"),
    softcopyDelivered(10,"softcopyDelivered"),
    hardcopyDelivered(11,"hardcopyDelivered"),
    //invoiceConfirm(12,"invoiceConfirm"),
    quotationSendDate(30,"quotationSendDate"),

    updateServiceType(96, "updateServiceType"),
    todoStatus(97,"todoStatus"),
    createTodo(98,"createTodo"),
    confirmMatrix(99,"confirmMatrix"),

    event(100,"event"),

    notifyAck(101,"syncSendResult"),

    /**
     * 广播的OrderEvent事件：例如to trf的OrderEvent
     */
    orderEvent(102,"orderEvent"),

    trfEvent(103,"trfEvent"),
    workFLowAudit(104,"workFLowAudit"),
    GPOPushDueDate(105,"GPOPushDueDate")
    ;
    private Integer status;
    private String code;

    KafkaActionType(Integer status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, KafkaActionType> maps = new HashMap<Integer, KafkaActionType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (KafkaActionType enu : KafkaActionType.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getCode();
    }

    public static KafkaActionType findKafkaActionType(String code) {
        for (KafkaActionType value : values()) {
            if(value.getCode().equalsIgnoreCase(code)){
                return value;
            }
        }
        return null;
    }
    public static KafkaActionType findKafkaByStatus(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status);
    }
}
