package com.sgs.preorder.facade.model.enums;

/**
 * 状态：0 初始化，1处理中，2通知成功，3通知失败
 * <AUTHOR>
 */
public enum NotifyMessageStatusEnum {
    init(0, "init"),
    processing(1, "processing"),
    success(2, "success"),
    fail(3, "fail"),
    ;
    private final int status;
    private final String message;

    NotifyMessageStatusEnum(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public int getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }


}
