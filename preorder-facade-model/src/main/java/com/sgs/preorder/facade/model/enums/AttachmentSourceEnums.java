package com.sgs.preorder.facade.model.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum AttachmentSourceEnums {
	MANUAL("Manual", "Manual"),
	ENQUIRY("Enquiry", "Enquiry"),
	PHOTO_TOOLS("PhotoTools", "PhotoTools");
	private String  status;
	private String value;

	AttachmentSourceEnums(String status, String value) {
		this.status = status;
		this.value = value;
	}

	public String getValue() {
		return value;
	}

	public String getStatus() {
		return status;
	}

	public static final Map<String, AttachmentSourceEnums> maps = new HashMap<String, AttachmentSourceEnums>() {
		private static final long serialVersionUID = -8986866330615001847L;
		{
			for (AttachmentSourceEnums enu : AttachmentSourceEnums.values()) {
				put(StringUtils.upperCase(enu.getStatus()), enu);
			}
		}
	};

	public static boolean check(String status, AttachmentSourceEnums caseType) {
		if (StringUtils.isBlank(status)) {
			return false;
		}
		return maps.get(StringUtils.upperCase(status)) == caseType;
	}

	public static String getValue(String status) {
		if (StringUtils.isBlank(status)) {
			return null;
		}
		AttachmentSourceEnums orderStatus = maps.get(status);
		return orderStatus == null ? null : orderStatus.getValue();
	}
}
