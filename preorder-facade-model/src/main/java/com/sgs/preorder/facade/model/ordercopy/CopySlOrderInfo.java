package com.sgs.preorder.facade.model.ordercopy;

import java.util.Date;

public class CopySlOrderInfo {
    /**
     *
     *
     */
    private Date cuttingExpectDueDate;
    /**
     * ExpectedOrderDueDate TIMESTAMP(19)<br>
     *
     */
    private Date jobExpectDueDate;
    /**
     *
     */
    private String trfTemplateOwner;
    /**
     *
     */
    private Date subcontractExpectDueDate;
    /**
     * Order Expect DueDate
     */
    private Date expectedOrderDueDate;

    public Date getCuttingExpectDueDate() {
        return cuttingExpectDueDate;
    }

    public void setCuttingExpectDueDate(Date cuttingExpectDueDate) {
        this.cuttingExpectDueDate = cuttingExpectDueDate;
    }

    public Date getJobExpectDueDate() {
        return jobExpectDueDate;
    }

    public void setJobExpectDueDate(Date jobExpectDueDate) {
        this.jobExpectDueDate = jobExpectDueDate;
    }

    public String getTrfTemplateOwner() {
        return trfTemplateOwner;
    }

    public void setTrfTemplateOwner(String trfTemplateOwner) {
        this.trfTemplateOwner = trfTemplateOwner;
    }

    public Date getSubcontractExpectDueDate() {
        return subcontractExpectDueDate;
    }

    public void setSubcontractExpectDueDate(Date subcontractExpectDueDate) {
        this.subcontractExpectDueDate = subcontractExpectDueDate;
    }

    public Date getExpectedOrderDueDate() {
        return expectedOrderDueDate;
    }

    public void setExpectedOrderDueDate(Date expectedOrderDueDate) {
        this.expectedOrderDueDate = expectedOrderDueDate;
    }
}
