package com.sgs.preorder.facade.model.enums;

public class DeliverWayEnums {
    public enum InvoiceDeliverWay{
        Other("Other", "Other"),
        ExpressFreightPrepaid("Express-Freight Prepaid", "Express-Freight Prepaid"),
        ExpressFreightCollect("Express-Freight Collect", "Express-Freight Collect"),
        ClientPickUp("Client Pick Up", "Client Pick Up"),
        ToBeDetermined("To Be Determined", "To Be Determined"),
        SoftcopyByEMail("Softcopy by E-Mail","Softcopy by E-Mail");
        private final String code;
        private final String name;

        InvoiceDeliverWay(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
    public enum HardCopyDeliverWay{
        Other("Other", "Other"),
        ExpressFreightPrepaid("Express-Freight Prepaid", "Express-Freight Prepaid"),
        ExpressFreightCollect("Express-Freight Collect", "Express-Freight Collect"),
        ClientPickUp("Client Pick Up", "Client Pick Up"),
        ToBeDetermined("To Be Determined", "To Be Determined");
        private final String code;
        private final String name;

        HardCopyDeliverWay(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
    public enum ReturnSampleWay{
        Other("Other", "Other"),
        ExpressFreightPrepaid("Express-Freight Prepaid", "Express-Freight Prepaid"),
        ExpressFreightCollect("Express-Freight Collect", "Express-Freight Collect"),
        ClientPickUp("Client Pick Up", "Client Pick Up"),
        ToBeDetermined("To Be Determined", "To Be Determined");


        private final String code;
        private final String name;
        ReturnSampleWay(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}
