package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ChargingStatusEnum {
//    YES(1, "YES"),
//    NO(0, "NO"),
    CONFIRMED(3,"CONFIRMED");

    private Integer status;
    private String message;

    ChargingStatusEnum(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, ChargingStatusEnum> maps = new HashMap<Integer, ChargingStatusEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ChargingStatusEnum enu : ChargingStatusEnum.values()) {
                put(enu.status, enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }
}
