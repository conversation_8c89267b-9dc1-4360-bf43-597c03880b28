package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum DraftReportRequiredEnum {
    YES(1, "YES"),
    NO(0, "NO");

    private Integer status;
    private String message;

    DraftReportRequiredEnum(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, DraftReportRequiredEnum> maps = new HashMap<Integer, DraftReportRequiredEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (DraftReportRequiredEnum enu : DraftReportRequiredEnum.values()) {
                put(enu.status, enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }
}
