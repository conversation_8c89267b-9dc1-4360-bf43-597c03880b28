package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ProductLineType {
    None(0,"None"),
    SL(1,true, "SL"),
    HL(2,true,"HL"),
    CChem<PERSON>ab(5,"CChemLab"),
    AUTO(10,true,"AUTO"),
    MR(15,true,"MR"),
    SUTA(22,true,"SUTA");

    private final int productLineId;
    private boolean sync = false;
    private final String productLineAbbr;

    ProductLineType(int productLineId, String productLineAbbr) {
        this.productLineId = productLineId;
        this.productLineAbbr = productLineAbbr;
    }

    ProductLineType(int productLineId, boolean sync, String productLineAbbr) {
        this(productLineId, productLineAbbr);
        this.sync = sync;
    }

    public int getProductLineId() {
        return productLineId;
    }

    public boolean isSync() {
        return sync;
    }

    public String getProductLineAbbr() {
        return productLineAbbr;
    }

    public static final Map<Integer, ProductLineType> maps = new HashMap<Integer, ProductLineType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ProductLineType enu : ProductLineType.values()) {
                put(enu.getProductLineId(), enu);
            }
        }
    };

    /**
     *
     * @param productLineId
     * @return
     */
    public static ProductLineType findType(Integer productLineId) {
        if (productLineId == null || !maps.containsKey(productLineId.intValue())) {
            return ProductLineType.None;
        }
        return maps.get(productLineId.intValue());
    }

    /**
     *
     * @param productLineId
     * @return
     */
    public static boolean checkSync(Integer productLineId){
        if (productLineId == null || !maps.containsKey(productLineId.intValue())) {
            return false;
        }
        ProductLineType productLineType = maps.get(productLineId);
        return productLineType.isSync();
    }

}
