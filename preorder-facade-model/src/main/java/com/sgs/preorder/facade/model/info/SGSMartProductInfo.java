package com.sgs.preorder.facade.model.info;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.preorder.facade.model.common.StringUtil;

import java.util.Date;
import java.util.List;

/*@JsonIgnoreProperties(value = {"handler"})*/
public class SGSMartProductInfo extends BaseProductLine {
    private String id;
    private String orderId;
    private String refSampleID;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productLibraryID;
    @JsonProperty("careLabelId")
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String careLabelInstanceID;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String communicationLogID;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerOrgannization1;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerOrgannization2;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerOrgannization3;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerOrgannizationCode1;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerOrgannizationCode2;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerOrgannizationCode3;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerAliase;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerSourcingOffice;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String countryOfOrigin;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String countryOfDestination;
    private String dFFFormID;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String supplier;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String supplierNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String factoryID;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String factoryName;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String firstFPUNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String firstPassFPUNo;
    /*@JsonInclude(JsonInclude.Include.NON_DEFAULT)*/
    private String firstTimeApplicationFlag;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String fPUNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String fPUReportNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String gPUNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String lotNo;
    /*@JsonInclude(JsonInclude.Include.NON_DEFAULT)*/
    private Integer noOfSample;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String otherSampleInformation;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String peformanceCode;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String pONo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String previousReportNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String trimReportNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String fabricReport;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productCategory1;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productCategory2;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String styleNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode1;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode2;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode3;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode4;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode5;
    //add by vincent 2018年7月4日 start
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode6;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode7;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode8;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode9;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode10;
    //add by vincent 2018年7月4日 end
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productColor;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productDescription;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productionStage;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String sampleID;
    /*@JsonInclude(JsonInclude.Include.NON_DEFAULT)*/
    private Date sampleReceivedDate;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String ageGroup;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String endUse1;
    //add by vincent 2018年7月4 start
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute1;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute2;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute3;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute4;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute5;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute6;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute7;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute8;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute9;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute10;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute11;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute12;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute13;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute14;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute15;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute16;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute17;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute18;
    //add by vincent 2018年7月4 end
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute1;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute2;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute3;
    //add by vincent 2018年7月4日 start
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute4;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute5;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute6;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute7;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute8;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute9;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute10;
    //add by vincent 2018年7月4日 end
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String construction;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String yarnCount;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String threadCount;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String fiberComposition;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String fiberWeight;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String fabricWidth;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String season;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String size;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialFinishing;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String collection;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String careLabelFlag;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String careLabel;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String careLabelWording;
    private String headerID;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productType;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productItemNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String cancelFlag;
    /**
     *
     */
    private Integer languageID;
    private String languageCode;

    private List<SGSMartProductInfo> productInstanceVos;

    public List<SGSMartProductInfo> getProductInstanceVos() {
        return productInstanceVos;
    }

    public void setProductInstanceVos(List<SGSMartProductInfo> productInstanceVos) {
        this.productInstanceVos = productInstanceVos;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getProductLibraryID() {
        return productLibraryID;
    }

    public void setProductLibraryID(String productLibraryID) {
        this.productLibraryID = productLibraryID;
    }

    public String getCareLabelInstanceID() {
        return careLabelInstanceID;
    }

    public void setCareLabelInstanceID(String careLabelInstanceID) {
        this.careLabelInstanceID = careLabelInstanceID;
    }

    public String getCommunicationLogID() {
        return communicationLogID;
    }

    public void setCommunicationLogID(String communicationLogID) {
        this.communicationLogID = communicationLogID;
    }

    public String getBuyerOrgannization1() {
        return buyerOrgannization1;
    }

    public void setBuyerOrgannization1(String buyerOrgannization1) {
        this.buyerOrgannization1 = buyerOrgannization1;
    }

    public String getBuyerOrgannization2() {
        return buyerOrgannization2;
    }

    public void setBuyerOrgannization2(String buyerOrgannization2) {
        this.buyerOrgannization2 = buyerOrgannization2;
    }

    public String getBuyerOrgannization3() {
        return buyerOrgannization3;
    }

    public void setBuyerOrgannization3(String buyerOrgannization3) {
        this.buyerOrgannization3 = buyerOrgannization3;
    }

    public String getBuyerOrgannizationCode1() {
        return buyerOrgannizationCode1;
    }

    public void setBuyerOrgannizationCode1(String buyerOrgannizationCode1) {
        this.buyerOrgannizationCode1 = buyerOrgannizationCode1;
    }

    public String getBuyerOrgannizationCode2() {
        return buyerOrgannizationCode2;
    }

    public void setBuyerOrgannizationCode2(String buyerOrgannizationCode2) {
        this.buyerOrgannizationCode2 = buyerOrgannizationCode2;
    }

    public String getBuyerOrgannizationCode3() {
        return buyerOrgannizationCode3;
    }

    public void setBuyerOrgannizationCode3(String buyerOrgannizationCode3) {
        this.buyerOrgannizationCode3 = buyerOrgannizationCode3;
    }

    public String getBuyerAliase() {
        return buyerAliase;
    }

    public void setBuyerAliase(String buyerAliase) {
        this.buyerAliase = buyerAliase;
    }

    public String getBuyerSourcingOffice() {
        return buyerSourcingOffice;
    }

    public void setBuyerSourcingOffice(String buyerSourcingOffice) {
        this.buyerSourcingOffice = buyerSourcingOffice;
    }

    public String getCountryOfOrigin() {
        return countryOfOrigin;
    }

    public void setCountryOfOrigin(String countryOfOrigin) {
        this.countryOfOrigin = countryOfOrigin;
    }

    public String getCountryOfDestination() {
        return countryOfDestination;
    }

    public void setCountryOfDestination(String countryOfDestination) {
        this.countryOfDestination = countryOfDestination;
    }

    public String getdFFFormID() {
        return dFFFormID;
    }

    public void setdFFFormID(String dFFFormID) {
        this.dFFFormID = dFFFormID;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public String getSupplierNo() {
        return supplierNo;
    }

    public void setSupplierNo(String supplierNo) {
        this.supplierNo = supplierNo;
    }

    public String getFactoryID() {
        return factoryID;
    }

    public void setFactoryID(String factoryID) {
        this.factoryID = factoryID;
    }

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }

    public String getFirstFPUNo() {
        return firstFPUNo;
    }

    public void setFirstFPUNo(String firstFPUNo) {
        this.firstFPUNo = firstFPUNo;
    }

    public String getFirstPassFPUNo() {
        return firstPassFPUNo;
    }

    public void setFirstPassFPUNo(String firstPassFPUNo) {
        this.firstPassFPUNo = firstPassFPUNo;
    }

    public String getFirstTimeApplicationFlag() {
        return firstTimeApplicationFlag;
    }

    public void setFirstTimeApplicationFlag(String firstTimeApplicationFlag) {
        this.firstTimeApplicationFlag = firstTimeApplicationFlag;
    }

    public String getfPUNo() {
        return fPUNo;
    }

    public void setfPUNo(String fPUNo) {
        this.fPUNo = fPUNo;
    }

    public String getfPUReportNo() {
        return fPUReportNo;
    }

    public void setfPUReportNo(String fPUReportNo) {
        this.fPUReportNo = fPUReportNo;
    }

    public String getgPUNo() {
        return gPUNo;
    }

    public void setgPUNo(String gPUNo) {
        this.gPUNo = gPUNo;
    }

    public String getLotNo() {
        return lotNo;
    }

    public void setLotNo(String lotNo) {
        this.lotNo = lotNo;
    }

    public Integer getNoOfSample() {
        return noOfSample;
    }

    public void setNoOfSample(Integer noOfSample) {
        this.noOfSample = noOfSample;
    }

    public String getOtherSampleInformation() {
        return otherSampleInformation;
    }

    public void setOtherSampleInformation(String otherSampleInformation) {
        this.otherSampleInformation = otherSampleInformation;
    }

    public String getPeformanceCode() {
        return peformanceCode;
    }

    public void setPeformanceCode(String peformanceCode) {
        this.peformanceCode = peformanceCode;
    }

    public String getpONo() {
        return pONo;
    }

    public void setpONo(String pONo) {
        this.pONo = pONo;
    }

    public String getPreviousReportNo() {
        return previousReportNo;
    }

    public void setPreviousReportNo(String previousReportNo) {
        this.previousReportNo = previousReportNo;
    }

    public String getTrimReportNo() {
        return trimReportNo;
    }

    public void setTrimReportNo(String trimReportNo) {
        this.trimReportNo = trimReportNo;
    }

    public String getFabricReport() {
        return fabricReport;
    }

    public void setFabricReport(String fabricReport) {
        this.fabricReport = fabricReport;
    }

    public String getProductCategory1() {
        return productCategory1;
    }

    public void setProductCategory1(String productCategory1) {
        this.productCategory1 = productCategory1;
    }

    public String getProductCategory2() {
        return productCategory2;
    }

    public void setProductCategory2(String productCategory2) {
        this.productCategory2 = productCategory2;
    }

    public String getStyleNo() {
        return styleNo;
    }

    public void setStyleNo(String styleNo) {
        this.styleNo = styleNo;
    }

    public String getRefCode1() {
        return refCode1;
    }

    public void setRefCode1(String refCode1) {
        this.refCode1 = refCode1;
    }

    public String getRefCode2() {
        return refCode2;
    }

    public void setRefCode2(String refCode2) {
        this.refCode2 = refCode2;
    }

    public String getRefCode3() {
        return refCode3;
    }

    public void setRefCode3(String refCode3) {
        this.refCode3 = refCode3;
    }

    public String getRefCode4() {
        return refCode4;
    }

    public void setRefCode4(String refCode4) {
        this.refCode4 = refCode4;
    }

    public String getRefCode5() {
        return refCode5;
    }

    public void setRefCode5(String refCode5) {
        this.refCode5 = refCode5;
    }

    public String getRefCode6() {
        return refCode6;
    }

    public void setRefCode6(String refCode6) {
        this.refCode6 = refCode6;
    }

    public String getRefCode7() {
        return refCode7;
    }

    public void setRefCode7(String refCode7) {
        this.refCode7 = refCode7;
    }

    public String getRefCode8() {
        return refCode8;
    }

    public void setRefCode8(String refCode8) {
        this.refCode8 = refCode8;
    }

    public String getRefCode9() {
        return refCode9;
    }

    public void setRefCode9(String refCode9) {
        this.refCode9 = refCode9;
    }

    public String getRefCode10() {
        return refCode10;
    }

    public void setRefCode10(String refCode10) {
        this.refCode10 = refCode10;
    }

    public String getProductColor() {
        return productColor;
    }

    public void setProductColor(String productColor) {
        this.productColor = productColor;
    }

    public String getProductDescription() {
        return productDescription;
    }

    public void setProductDescription(String productDescription) {
        this.productDescription = productDescription;
    }

    public String getProductionStage() {
        return productionStage;
    }

    public void setProductionStage(String productionStage) {
        this.productionStage = productionStage;
    }

    public String getSampleID() {
        return sampleID;
    }

    public void setSampleID(String sampleID) {
        this.sampleID = sampleID;
    }

    public Date getSampleReceivedDate() {
        return sampleReceivedDate;
    }

    public void setSampleReceivedDate(Date sampleReceivedDate) {
        this.sampleReceivedDate = sampleReceivedDate;
    }

    public String getAgeGroup() {
        return ageGroup;
    }

    public void setAgeGroup(String ageGroup) {
        this.ageGroup = ageGroup;
    }

    public String getEndUse1() {
        return endUse1;
    }

    public void setEndUse1(String endUse1) {
        this.endUse1 = endUse1;
    }

    public String getSpecialCustomerAttribute1() {
        return specialCustomerAttribute1;
    }

    public void setSpecialCustomerAttribute1(String specialCustomerAttribute1) {
        this.specialCustomerAttribute1 = specialCustomerAttribute1;
    }

    public String getSpecialCustomerAttribute2() {
        return specialCustomerAttribute2;
    }

    public void setSpecialCustomerAttribute2(String specialCustomerAttribute2) {
        this.specialCustomerAttribute2 = specialCustomerAttribute2;
    }

    public String getSpecialCustomerAttribute3() {
        return specialCustomerAttribute3;
    }

    public void setSpecialCustomerAttribute3(String specialCustomerAttribute3) {
        this.specialCustomerAttribute3 = specialCustomerAttribute3;
    }

    public String getSpecialCustomerAttribute4() {
        return specialCustomerAttribute4;
    }

    public void setSpecialCustomerAttribute4(String specialCustomerAttribute4) {
        this.specialCustomerAttribute4 = specialCustomerAttribute4;
    }

    public String getSpecialCustomerAttribute5() {
        return specialCustomerAttribute5;
    }

    public void setSpecialCustomerAttribute5(String specialCustomerAttribute5) {
        this.specialCustomerAttribute5 = specialCustomerAttribute5;
    }

    public String getSpecialCustomerAttribute6() {
        return specialCustomerAttribute6;
    }

    public void setSpecialCustomerAttribute6(String specialCustomerAttribute6) {
        this.specialCustomerAttribute6 = specialCustomerAttribute6;
    }

    public String getSpecialCustomerAttribute7() {
        return specialCustomerAttribute7;
    }

    public void setSpecialCustomerAttribute7(String specialCustomerAttribute7) {
        this.specialCustomerAttribute7 = specialCustomerAttribute7;
    }

    public String getSpecialCustomerAttribute8() {
        return specialCustomerAttribute8;
    }

    public void setSpecialCustomerAttribute8(String specialCustomerAttribute8) {
        this.specialCustomerAttribute8 = specialCustomerAttribute8;
    }

    public String getSpecialCustomerAttribute9() {
        return specialCustomerAttribute9;
    }

    public void setSpecialCustomerAttribute9(String specialCustomerAttribute9) {
        this.specialCustomerAttribute9 = specialCustomerAttribute9;
    }

    public String getSpecialCustomerAttribute10() {
        return specialCustomerAttribute10;
    }

    public void setSpecialCustomerAttribute10(String specialCustomerAttribute10) {
        this.specialCustomerAttribute10 = specialCustomerAttribute10;
    }

    public String getSpecialCustomerAttribute11() {
        return specialCustomerAttribute11;
    }

    public void setSpecialCustomerAttribute11(String specialCustomerAttribute11) {
        this.specialCustomerAttribute11 = specialCustomerAttribute11;
    }

    public String getSpecialCustomerAttribute12() {
        return specialCustomerAttribute12;
    }

    public void setSpecialCustomerAttribute12(String specialCustomerAttribute12) {
        this.specialCustomerAttribute12 = specialCustomerAttribute12;
    }

    public String getSpecialCustomerAttribute13() {
        return specialCustomerAttribute13;
    }

    public void setSpecialCustomerAttribute13(String specialCustomerAttribute13) {
        this.specialCustomerAttribute13 = specialCustomerAttribute13;
    }

    public String getSpecialCustomerAttribute14() {
        return specialCustomerAttribute14;
    }

    public void setSpecialCustomerAttribute14(String specialCustomerAttribute14) {
        this.specialCustomerAttribute14 = specialCustomerAttribute14;
    }

    public String getSpecialCustomerAttribute15() {
        return specialCustomerAttribute15;
    }

    public void setSpecialCustomerAttribute15(String specialCustomerAttribute15) {
        this.specialCustomerAttribute15 = specialCustomerAttribute15;
    }

    public String getSpecialCustomerAttribute16() {
        return specialCustomerAttribute16;
    }

    public void setSpecialCustomerAttribute16(String specialCustomerAttribute16) {
        this.specialCustomerAttribute16 = specialCustomerAttribute16;
    }

    public String getSpecialCustomerAttribute17() {
        return specialCustomerAttribute17;
    }

    public void setSpecialCustomerAttribute17(String specialCustomerAttribute17) {
        this.specialCustomerAttribute17 = specialCustomerAttribute17;
    }

    public String getSpecialCustomerAttribute18() {
        return specialCustomerAttribute18;
    }

    public void setSpecialCustomerAttribute18(String specialCustomerAttribute18) {
        this.specialCustomerAttribute18 = specialCustomerAttribute18;
    }

    public String getSpecialProductAttribute1() {
        return specialProductAttribute1;
    }

    public void setSpecialProductAttribute1(String specialProductAttribute1) {
        this.specialProductAttribute1 = specialProductAttribute1;
    }

    public String getSpecialProductAttribute2() {
        return specialProductAttribute2;
    }

    public void setSpecialProductAttribute2(String specialProductAttribute2) {
        this.specialProductAttribute2 = specialProductAttribute2;
    }

    public String getSpecialProductAttribute3() {
        return specialProductAttribute3;
    }

    public void setSpecialProductAttribute3(String specialProductAttribute3) {
        this.specialProductAttribute3 = specialProductAttribute3;
    }

    public String getSpecialProductAttribute4() {
        return specialProductAttribute4;
    }

    public void setSpecialProductAttribute4(String specialProductAttribute4) {
        this.specialProductAttribute4 = specialProductAttribute4;
    }

    public String getSpecialProductAttribute5() {
        return specialProductAttribute5;
    }

    public void setSpecialProductAttribute5(String specialProductAttribute5) {
        this.specialProductAttribute5 = specialProductAttribute5;
    }

    public String getSpecialProductAttribute6() {
        return specialProductAttribute6;
    }

    public void setSpecialProductAttribute6(String specialProductAttribute6) {
        this.specialProductAttribute6 = specialProductAttribute6;
    }

    public String getSpecialProductAttribute7() {
        return specialProductAttribute7;
    }

    public void setSpecialProductAttribute7(String specialProductAttribute7) {
        this.specialProductAttribute7 = specialProductAttribute7;
    }

    public String getSpecialProductAttribute8() {
        return specialProductAttribute8;
    }

    public void setSpecialProductAttribute8(String specialProductAttribute8) {
        this.specialProductAttribute8 = specialProductAttribute8;
    }

    public String getSpecialProductAttribute9() {
        return specialProductAttribute9;
    }

    public void setSpecialProductAttribute9(String specialProductAttribute9) {
        this.specialProductAttribute9 = specialProductAttribute9;
    }

    public String getSpecialProductAttribute10() {
        return specialProductAttribute10;
    }

    public void setSpecialProductAttribute10(String specialProductAttribute10) {
        this.specialProductAttribute10 = specialProductAttribute10;
    }

    public String getConstruction() {
        return construction;
    }

    public void setConstruction(String construction) {
        this.construction = construction;
    }

    public String getYarnCount() {
        return yarnCount;
    }

    public void setYarnCount(String yarnCount) {
        this.yarnCount = yarnCount;
    }

    public String getThreadCount() {
        return threadCount;
    }

    public void setThreadCount(String threadCount) {
        this.threadCount = threadCount;
    }

    public String getFiberComposition() {
        return fiberComposition;
    }

    public void setFiberComposition(String fiberComposition) {
        this.fiberComposition = fiberComposition;
    }

    public String getFiberWeight() {
        return fiberWeight;
    }

    public void setFiberWeight(String fiberWeight) {
        this.fiberWeight = fiberWeight;
    }

    public String getFabricWidth() {
        return fabricWidth;
    }

    public void setFabricWidth(String fabricWidth) {
        this.fabricWidth = fabricWidth;
    }

    public String getSeason() {
        return season;
    }

    public void setSeason(String season) {
        this.season = season;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getSpecialFinishing() {
        return specialFinishing;
    }

    public void setSpecialFinishing(String specialFinishing) {
        this.specialFinishing = specialFinishing;
    }

    public String getCollection() {
        return collection;
    }

    public void setCollection(String collection) {
        this.collection = collection;
    }

    public String getCareLabelFlag() {
        return careLabelFlag;
    }

    public void setCareLabelFlag(String careLabelFlag) {
        this.careLabelFlag = careLabelFlag;
    }

    public String getCareLabel() {
        return careLabel;
    }

    public void setCareLabel(String careLabel) {
        this.careLabel = careLabel;
    }

    public String getCareLabelWording() {
        return careLabelWording;
    }

    public void setCareLabelWording(String careLabelWording) {
        this.careLabelWording = careLabelWording;
    }

    public String getHeaderID() {
        return headerID;
    }

    public void setHeaderID(String headerID) {
        this.headerID = headerID;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getProductItemNo() {
        return productItemNo;
    }

    public void setProductItemNo(String productItemNo) {
        this.productItemNo = productItemNo;
    }

    public String getCancelFlag() {
        return cancelFlag;
    }

    public void setCancelFlag(String cancelFlag) {
        this.cancelFlag = cancelFlag;
    }

   

    public Integer getLanguageID() {
		return languageID;
	}

	public void setLanguageID(Integer languageID) {
		this.languageID = languageID;
	}

	public String getRefSampleID() {
        return refSampleID;
    }

    public void setRefSampleID(String refSampleID) {
        this.refSampleID = refSampleID;
    }
	
	public String getLanguageCode() {
		return languageCode;
	}

	public void setLanguageCode(String languageCode) {
		this.languageCode = languageCode;
	}

	/**
     *
     * @return
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int hashCode = 1;

        hashCode = prime * hashCode + (StringUtil.hashCode(orderId));
        hashCode = prime * hashCode + (StringUtil.hashCode(productLibraryID));
        hashCode = prime * hashCode + (StringUtil.hashCode(careLabelInstanceID));


        hashCode = prime * hashCode + (StringUtil.hashCode(communicationLogID));
        hashCode = prime * hashCode + (StringUtil.hashCode(buyerOrgannization1));
        hashCode = prime * hashCode + (StringUtil.hashCode(buyerOrgannization2));
        hashCode = prime * hashCode + (StringUtil.hashCode(buyerOrgannization3));
        hashCode = prime * hashCode + (StringUtil.hashCode(buyerOrgannizationCode1));
        hashCode = prime * hashCode + (StringUtil.hashCode(buyerOrgannizationCode2));
        hashCode = prime * hashCode + (StringUtil.hashCode(buyerOrgannizationCode3));

        hashCode = prime * hashCode + (StringUtil.hashCode(buyerAliase));
        hashCode = prime * hashCode + (StringUtil.hashCode(buyerSourcingOffice));
        hashCode = prime * hashCode + (StringUtil.hashCode(countryOfOrigin));
        hashCode = prime * hashCode + (StringUtil.hashCode(countryOfDestination));
        hashCode = prime * hashCode + (StringUtil.hashCode(dFFFormID));//
        hashCode = prime * hashCode + (StringUtil.hashCode(supplier));
        hashCode = prime * hashCode + (StringUtil.hashCode(supplierNo));
        hashCode = prime * hashCode + (StringUtil.hashCode(factoryID));
        hashCode = prime * hashCode + (StringUtil.hashCode(factoryName));
        hashCode = prime * hashCode + (StringUtil.hashCode(firstFPUNo));
        hashCode = prime * hashCode + (StringUtil.hashCode(firstPassFPUNo));
        hashCode = prime * hashCode + (StringUtil.hashCode(firstTimeApplicationFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(fPUNo));//
        hashCode = prime * hashCode + (StringUtil.hashCode(fPUReportNo));//
        hashCode = prime * hashCode + (StringUtil.hashCode(gPUNo));
        hashCode = prime * hashCode + (StringUtil.hashCode(lotNo));

        hashCode = prime * hashCode + (StringUtil.hashCode(noOfSample));
        hashCode = prime * hashCode + (StringUtil.hashCode(otherSampleInformation));
        hashCode = prime * hashCode + (StringUtil.hashCode(peformanceCode));
        hashCode = prime * hashCode + (StringUtil.hashCode(pONo));
        hashCode = prime * hashCode + (StringUtil.hashCode(previousReportNo));
        hashCode = prime * hashCode + (StringUtil.hashCode(trimReportNo));
        hashCode = prime * hashCode + (StringUtil.hashCode(fabricReport));
        hashCode = prime * hashCode + (StringUtil.hashCode(productCategory1));
        hashCode = prime * hashCode + (StringUtil.hashCode(productCategory2));
        hashCode = prime * hashCode + (StringUtil.hashCode(styleNo));

        hashCode = prime * hashCode + (StringUtil.hashCode(refCode1));
        hashCode = prime * hashCode + (StringUtil.hashCode(refCode2));
        hashCode = prime * hashCode + (StringUtil.hashCode(refCode3));
        hashCode = prime * hashCode + (StringUtil.hashCode(refCode4));
        hashCode = prime * hashCode + (StringUtil.hashCode(refCode5));

        hashCode = prime * hashCode + (StringUtil.hashCode(productColor));
        hashCode = prime * hashCode + (StringUtil.hashCode(productDescription));
        hashCode = prime * hashCode + (StringUtil.hashCode(productionStage));
        hashCode = prime * hashCode + (StringUtil.hashCode(sampleID));
        hashCode = prime * hashCode + (StringUtil.hashCode(sampleReceivedDate));
        hashCode = prime * hashCode + (StringUtil.hashCode(ageGroup));
        hashCode = prime * hashCode + (StringUtil.hashCode(endUse1));

        hashCode = prime * hashCode + (StringUtil.hashCode(specialProductAttribute1));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialProductAttribute2));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialProductAttribute3));
        hashCode = prime * hashCode + (StringUtil.hashCode(construction));
        hashCode = prime * hashCode + (StringUtil.hashCode(yarnCount));
        hashCode = prime * hashCode + (StringUtil.hashCode(threadCount));
        hashCode = prime * hashCode + (StringUtil.hashCode(fiberComposition));
        hashCode = prime * hashCode + (StringUtil.hashCode(fiberWeight));
        hashCode = prime * hashCode + (StringUtil.hashCode(fabricWidth));
        hashCode = prime * hashCode + (StringUtil.hashCode(season));
        hashCode = prime * hashCode + (StringUtil.hashCode(size));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialFinishing));
        hashCode = prime * hashCode + (StringUtil.hashCode(collection));
        hashCode = prime * hashCode + (StringUtil.hashCode(careLabelFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(careLabel));
        hashCode = prime * hashCode + (StringUtil.hashCode(careLabelWording));
        hashCode = prime * hashCode + (StringUtil.hashCode(headerID));
        hashCode = prime * hashCode + (StringUtil.hashCode(productType));
        hashCode = prime * hashCode + (StringUtil.hashCode(productItemNo));
        hashCode = prime * hashCode + (StringUtil.hashCode(cancelFlag));

        hashCode = prime * hashCode + (StringUtil.hashCode(refCode6));
        hashCode = prime * hashCode + (StringUtil.hashCode(refCode7));
        hashCode = prime * hashCode + (StringUtil.hashCode(refCode8));
        hashCode = prime * hashCode + (StringUtil.hashCode(refCode9));
        hashCode = prime * hashCode + (StringUtil.hashCode(refCode10));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute1));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute2));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute3));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute4));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute5));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute6));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute7));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute8));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute9));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute10));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute11));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute12));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute13));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute14));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute15));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute16));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute17));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialCustomerAttribute18));

        hashCode = prime * hashCode + (StringUtil.hashCode(specialProductAttribute4));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialProductAttribute5));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialProductAttribute6));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialProductAttribute7));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialProductAttribute8));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialProductAttribute9));
        hashCode = prime * hashCode + (StringUtil.hashCode(specialProductAttribute10));

        hashCode = prime * hashCode + (StringUtil.hashCode(languageID));
        hashCode = prime * hashCode + (StringUtil.hashCode(refSampleID));

        return hashCode;
    }
}
