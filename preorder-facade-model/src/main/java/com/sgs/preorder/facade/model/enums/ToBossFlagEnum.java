package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ToBossFlagEnum {
    YES(1, "YES"),
    Complated(2, "Complated"),
    NO(0, "NO");

    private Integer status;
    private String message;

    ToBossFlagEnum(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, ToBossFlagEnum> maps = new HashMap<Integer, ToBossFlagEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ToBossFlagEnum enu : ToBossFlagEnum.values()) {
                put(enu.status, enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }
}
