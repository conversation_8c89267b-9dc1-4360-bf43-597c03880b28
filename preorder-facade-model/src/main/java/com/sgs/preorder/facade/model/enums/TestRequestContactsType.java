package com.sgs.preorder.facade.model.enums;

public enum TestRequestContactsType {
    SOFTCOPYTO((byte) 1, "softCopyTo"),
    HARDCOPYTO((byte) 2, "hardCopyTo"),
    REPORT((byte) 3, "report"),
    RETURNSAMPLE((byte) 4, "returnSample"),
    INVOICE((byte) 5, "invoice"),
    PRELAM((byte) 6, "prelam"),
    PRELAMCC((byte) 7, "prelamCc"),
    SOFTCOPYCC((byte) 8, "softcopyCc");

    private final byte code;
    private final String name;

    TestRequestContactsType(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public byte getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
