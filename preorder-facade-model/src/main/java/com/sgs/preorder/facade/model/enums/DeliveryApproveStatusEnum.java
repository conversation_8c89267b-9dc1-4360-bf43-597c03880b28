package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum DeliveryApproveStatusEnum {
    New(1, "New"),
    Approved(2, "Approved"),
    Reject(3, "Reject")
    ;

    private Integer status;
    private String message;

    DeliveryApproveStatusEnum(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, DeliveryApproveStatusEnum> maps = new HashMap<Integer, DeliveryApproveStatusEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (DeliveryApproveStatusEnum enu : DeliveryApproveStatusEnum.values()) {
                put(enu.status, enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }
}
