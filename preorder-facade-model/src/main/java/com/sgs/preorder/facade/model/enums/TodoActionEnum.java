package com.sgs.preorder.facade.model.enums;

/**
 * <AUTHOR>
 */

public enum TodoActionEnum {
    TODO("1", "todo"),
    CANCEL("2", "cancel"),
    COMPLETE("3", "complete"),
    RENEW("4", "renew"),
    ;

    private String code;
    private String name;

    public String getCode() {
        return code;
    }


    public String getName() {
        return name;
    }


    TodoActionEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TodoActionEnum getByCode(String code) {
        for (TodoActionEnum value : TodoActionEnum.values()) {
            if (value.code.equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
