package com.sgs.preorder.facade.model.info;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class BossOrderPO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3515661282762015230L;

	private String bossOrderNo;
	private String bossOrderStatus;
	private String trueOrderNo;
	private BigDecimal totalAmount;
	private int deleteFlag;
	private String customerCompany;
	private String errorMessage;
	private String hattribute1;
	private String hattribute2;
	private String hattribute3;
	private String hattribute4;
	private String hattribute5;
	private String hattribute6;
	private String hattribute7;
	private String hattribute8;
	private String hattribute9;
	private String hattribute10;
	private String hattribute11;
	private String hattribute12;
	private String hattribute13;
	private String hattribute14;
	private String hattribute15;
	private String hattribute16;
	private String hbookedFlag;
	private String hcancelledFlag;
	private String hclosedFlag;
	private String hcontext;
	private String hconversionType;
	private long hinvoicetoorgID;
	private String hoperationCode;
	private String horderCategory;
	private Date horderedDate;
	private String horderSourceName;
	private String horderTypeName;
	private String horderNumberToAddLine;
	private String hpaymentTermName;
	private String hpriceListName;
	private int hpushStatus;
	private String hsalesrepID;
	private String hshipFromOrgName;
	private long hshiptoorgID;
	private String hsoldFromOrgName;
	private String hsoldToCust;
	private String htaxFlag;
	private String htransactionalCurrCode;
	private String orgName;
	private boolean successFlag;
	private String reviseReason;
	private String customerID;
	private String storeID;
	private String shipName;
	private String billName;
	private String BuyerName;
	private String hcustomerPoNumber;
	
	//关联表字段
	private String generalOrderID;
	private String relID;
	//Invice相关信息
	private String bossInvoiceNo;
	private String bossInvoiceDate;
	//新增字段 deliver和contact相关信息
	private long hdelivertoorgID;
	private String deliverName;
	private long hbillToContactID;
	private String hbillContactName;
	private long hshipToContactID;
	private String hshipContactName;
	private long hdeliverToContactID;
	private String hdeliverContactName;
	//新增字段 在详情页面关联出对应的orderNo
	private String orderNo;
	//新增字段 在详情页面关联出对应的caseType
	private String caseType;

	//新增字段
	private String createByBossUser;
	//新增字段
	private String createByBossUserResponsibility;
	//新增字段
	private String shipToAddress;
	//新增字段
	private String billToAddress;
	//新增字段----对于1个报告号的，不传值，多单合并产生的多个报告号，用分号隔开传值；
	private String certificateReportNB;

	private String deliverToAddress;

	//新增字段 ---当前ToBoss是哪一个版本
	private Integer versionId;

	//新增字段 ---当前ToBoss对应的quotationHead
	private String quotationHeadId;


	private String id;
	private Date createdDate;
	private String createdBy;
	private Date modifiedDate;
	private String modifiedBy;
	private int activeIndicator;
	private String hSalesPerson;



	public BossOrderPO() {
	}

	public String getQuotationHeadId() {
		return quotationHeadId;
	}

	public void setQuotationHeadId(String quotationHeadId) {
		this.quotationHeadId = quotationHeadId;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public String getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(String modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public int getActiveIndicator() {
		return activeIndicator;
	}

	public void setActiveIndicator(int activeIndicator) {
		this.activeIndicator = activeIndicator;
	}

	public Integer getVersionId() {
		return versionId;
	}

	public void setVersionId(Integer versionId) {
		this.versionId = versionId;
	}

	public String getDeliverToAddress() {
		return deliverToAddress;
	}

	public void setDeliverToAddress(String deliverToAddress) {
		this.deliverToAddress = deliverToAddress;
	}

	public String getCertificateReportNB() {
		return certificateReportNB;
	}

	public void setCertificateReportNB(String certificateReportNB) {
		this.certificateReportNB = certificateReportNB;
	}

	public String getShipToAddress() {
		return shipToAddress;
	}

	public void setShipToAddress(String shipToAddress) {
		this.shipToAddress = shipToAddress;
	}

	public String getBillToAddress() {
		return billToAddress;
	}

	public void setBillToAddress(String billToAddress) {
		this.billToAddress = billToAddress;
	}

	public String getCreateByBossUser() {
		return createByBossUser;
	}

	public void setCreateByBossUser(String createByBossUser) {
		this.createByBossUser = createByBossUser;
	}

	public String getCreateByBossUserResponsibility() {
		return createByBossUserResponsibility;
	}

	public void setCreateByBossUserResponsibility(String createByBossUserResponsibility) {
		this.createByBossUserResponsibility = createByBossUserResponsibility;
	}

	public String getCaseType() {
		return caseType;
	}
	public void setCaseType(String caseType) {
		this.caseType = caseType;
	}
	public String getHbillContactName() {
		return hbillContactName;
	}
	public void setHbillContactName(String hbillContactName) {
		this.hbillContactName = hbillContactName;
	}
	public String getHshipContactName() {
		return hshipContactName;
	}
	public void setHshipContactName(String hshipContactName) {
		this.hshipContactName = hshipContactName;
	}
	public String getOrderNo() {
		return orderNo;
	}
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}
	public long getHdelivertoorgID() {
		return hdelivertoorgID;
	}
	public void setHdelivertoorgID(long hdelivertoorgID) {
		this.hdelivertoorgID = hdelivertoorgID;
	}
	public String getDeliverName() {
		return deliverName;
	}
	public void setDeliverName(String deliverName) {
		this.deliverName = deliverName;
	}
	public long getHbillToContactID() {
		return hbillToContactID;
	}
	public void setHbillToContactID(long hbillToContactID) {
		this.hbillToContactID = hbillToContactID;
	}
	public long getHshipToContactID() {
		return hshipToContactID;
	}
	public void setHshipToContactID(long hshipToContactID) {
		this.hshipToContactID = hshipToContactID;
	}
	public long getHdeliverToContactID() {
		return hdeliverToContactID;
	}
	public void setHdeliverToContactID(long hdeliverToContactID) {
		this.hdeliverToContactID = hdeliverToContactID;
	}
	public String getHdeliverContactName() {
		return hdeliverContactName;
	}
	public void setHdeliverContactName(String hdeliverContactName) {
		this.hdeliverContactName = hdeliverContactName;
	}
	public String getBossInvoiceNo() {
		return bossInvoiceNo;
	}
	public void setBossInvoiceNo(String bossInvoiceNo) {
		this.bossInvoiceNo = bossInvoiceNo;
	}
	public String getBossInvoiceDate() {
		return bossInvoiceDate;
	}
	public void setBossInvoiceDate(String bossInvoiceDate) {
		this.bossInvoiceDate = bossInvoiceDate;
	}
	public String getRelID() {
		return relID;
	}
	public void setRelID(String relID) {
		this.relID = relID;
	}
	public String getHcustomerPoNumber() {
		return hcustomerPoNumber;
	}
	public void setHcustomerPoNumber(String hcustomerPoNumber) {
		this.hcustomerPoNumber = hcustomerPoNumber;
	}
	public String getBossOrderNo() {
		return bossOrderNo;
	}
	public void setBossOrderNo(String bossOrderNo) {
		this.bossOrderNo = bossOrderNo;
	}
	public String getBossOrderStatus() {
		return bossOrderStatus;
	}
	public void setBossOrderStatus(String bossOrderStatus) {
		this.bossOrderStatus = bossOrderStatus;
	}
	public String getTrueOrderNo() {
		return trueOrderNo;
	}
	public void setTrueOrderNo(String trueOrderNo) {
		this.trueOrderNo = trueOrderNo;
	}
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}
	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}
	public int getDeleteFlag() {
		return deleteFlag;
	}
	public void setDeleteFlag(int deleteFlag) {
		this.deleteFlag = deleteFlag;
	}
	public String getCustomerCompany() {
		return customerCompany;
	}
	public void setCustomerCompany(String customerCompany) {
		this.customerCompany = customerCompany;
	}
	public String getErrorMessage() {
		return errorMessage;
	}
	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}
	public String getHattribute1() {
		return hattribute1;
	}
	public void setHattribute1(String hattribute1) {
		this.hattribute1 = hattribute1;
	}
	public String getHattribute2() {
		return hattribute2;
	}
	public void setHattribute2(String hattribute2) {
		this.hattribute2 = hattribute2;
	}
	public String getHattribute3() {
		return hattribute3;
	}
	public void setHattribute3(String hattribute3) {
		this.hattribute3 = hattribute3;
	}
	public String getHattribute4() {
		return hattribute4;
	}
	public void setHattribute4(String hattribute4) {
		this.hattribute4 = hattribute4;
	}
	public String getHattribute5() {
		return hattribute5;
	}
	public void setHattribute5(String hattribute5) {
		this.hattribute5 = hattribute5;
	}
	public String getHattribute6() {
		return hattribute6;
	}
	public void setHattribute6(String hattribute6) {
		this.hattribute6 = hattribute6;
	}
	public String getHattribute7() {
		return hattribute7;
	}
	public void setHattribute7(String hattribute7) {
		this.hattribute7 = hattribute7;
	}
	public String getHattribute8() {
		return hattribute8;
	}
	public void setHattribute8(String hattribute8) {
		this.hattribute8 = hattribute8;
	}
	public String getHattribute9() {
		return hattribute9;
	}
	public void setHattribute9(String hattribute9) {
		this.hattribute9 = hattribute9;
	}
	public String getHattribute10() {
		return hattribute10;
	}
	public void setHattribute10(String hattribute10) {
		this.hattribute10 = hattribute10;
	}
	public String getHattribute11() {
		return hattribute11;
	}
	public void setHattribute11(String hattribute11) {
		this.hattribute11 = hattribute11;
	}
	public String getHattribute12() {
		return hattribute12;
	}
	public void setHattribute12(String hattribute12) {
		this.hattribute12 = hattribute12;
	}
	public String getHattribute13() {
		return hattribute13;
	}
	public void setHattribute13(String hattribute13) {
		this.hattribute13 = hattribute13;
	}
	public String getHattribute14() {
		return hattribute14;
	}
	public void setHattribute14(String hattribute14) {
		this.hattribute14 = hattribute14;
	}
	public String getHattribute15() {
		return hattribute15;
	}
	public void setHattribute15(String hattribute15) {
		this.hattribute15 = hattribute15;
	}
	public String getHattribute16() {
		return hattribute16;
	}
	public void setHattribute16(String hattribute16) {
		this.hattribute16 = hattribute16;
	}
	public String getHbookedFlag() {
		return hbookedFlag;
	}
	public void setHbookedFlag(String hbookedFlag) {
		this.hbookedFlag = hbookedFlag;
	}
	public String getHcancelledFlag() {
		return hcancelledFlag;
	}
	public void setHcancelledFlag(String hcancelledFlag) {
		this.hcancelledFlag = hcancelledFlag;
	}
	public String getHclosedFlag() {
		return hclosedFlag;
	}
	public void setHclosedFlag(String hclosedFlag) {
		this.hclosedFlag = hclosedFlag;
	}
	public String getHcontext() {
		return hcontext;
	}
	public void setHcontext(String hcontext) {
		this.hcontext = hcontext;
	}
	public String getHconversionType() {
		return hconversionType;
	}
	public void setHconversionType(String hconversionType) {
		this.hconversionType = hconversionType;
	}
	public long getHinvoicetoorgID() {
		return hinvoicetoorgID;
	}
	public void setHinvoicetoorgID(long hinvoicetoorgID) {
		this.hinvoicetoorgID = hinvoicetoorgID;
	}
	public long getHshiptoorgID() {
		return hshiptoorgID;
	}
	public void setHshiptoorgID(long hshiptoorgID) {
		this.hshiptoorgID = hshiptoorgID;
	}
	public String getHoperationCode() {
		return hoperationCode;
	}
	public void setHoperationCode(String hoperationCode) {
		this.hoperationCode = hoperationCode;
	}
	public String getHorderCategory() {
		return horderCategory;
	}
	public void setHorderCategory(String horderCategory) {
		this.horderCategory = horderCategory;
	}
	public Date getHorderedDate() {
		return horderedDate;
	}
	public void setHorderedDate(Date horderedDate) {
		this.horderedDate = horderedDate;
	}
	public String getHorderSourceName() {
		return horderSourceName;
	}
	public void setHorderSourceName(String horderSourceName) {
		this.horderSourceName = horderSourceName;
	}
	public String getHorderTypeName() {
		return horderTypeName;
	}
	public void setHorderTypeName(String horderTypeName) {
		this.horderTypeName = horderTypeName;
	}
	public String getHorderNumberToAddLine() {
		return horderNumberToAddLine;
	}
	public void setHorderNumberToAddLine(String horderNumberToAddLine) {
		this.horderNumberToAddLine = horderNumberToAddLine;
	}
	public String getHpaymentTermName() {
		return hpaymentTermName;
	}
	public void setHpaymentTermName(String hpaymentTermName) {
		this.hpaymentTermName = hpaymentTermName;
	}
	public String getHpriceListName() {
		return hpriceListName;
	}
	public void setHpriceListName(String hpriceListName) {
		this.hpriceListName = hpriceListName;
	}
	public int getHpushStatus() {
		return hpushStatus;
	}
	public void setHpushStatus(int hpushStatus) {
		this.hpushStatus = hpushStatus;
	}
	public String getHsalesrepID() {
		return hsalesrepID;
	}
	public void setHsalesrepID(String hsalesrepID) {
		this.hsalesrepID = hsalesrepID;
	}
	public String getHshipFromOrgName() {
		return hshipFromOrgName;
	}
	public void setHshipFromOrgName(String hshipFromOrgName) {
		this.hshipFromOrgName = hshipFromOrgName;
	}

	public String getHsoldFromOrgName() {
		return hsoldFromOrgName;
	}
	public void setHsoldFromOrgName(String hsoldFromOrgName) {
		this.hsoldFromOrgName = hsoldFromOrgName;
	}
	public String getHsoldToCust() {
		return hsoldToCust;
	}
	public void setHsoldToCust(String hsoldToCust) {
		this.hsoldToCust = hsoldToCust;
	}
	public String getHtaxFlag() {
		return htaxFlag;
	}
	public void setHtaxFlag(String htaxFlag) {
		this.htaxFlag = htaxFlag;
	}
	public String getHtransactionalCurrCode() {
		return htransactionalCurrCode;
	}
	public void setHtransactionalCurrCode(String htransactionalCurrCode) {
		this.htransactionalCurrCode = htransactionalCurrCode;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public boolean isSuccessFlag() {
		return successFlag;
	}
	public void setSuccessFlag(boolean successFlag) {
		this.successFlag = successFlag;
	}
	public String getReviseReason() {
		return reviseReason;
	}
	public void setReviseReason(String reviseReason) {
		this.reviseReason = reviseReason;
	}
	public String getCustomerID() {
		return customerID;
	}
	public void setCustomerID(String customerID) {
		this.customerID = customerID;
	}
	public String getStoreID() {
		return storeID;
	}
	public void setStoreID(String storeID) {
		this.storeID = storeID;
	}
	public String getShipName() {
		return shipName;
	}
	public void setShipName(String shipName) {
		this.shipName = shipName;
	}
	public String getBillName() {
		return billName;
	}
	public void setBillName(String billName) {
		this.billName = billName;
	}
	public String getBuyerName() {return BuyerName;}
	public void setBuyerName(String buyerName) {BuyerName = buyerName;}
	public String getGeneralOrderID() {
		return generalOrderID;
	}
	public void setGeneralOrderID(String generalOrderID) {
		this.generalOrderID = generalOrderID;
	}

	public String getHSalesPerson() {
		return hSalesPerson;
	}

	public void setHSalesPerson(String hSalesPerson) {
		this.hSalesPerson = hSalesPerson;
	}
}
