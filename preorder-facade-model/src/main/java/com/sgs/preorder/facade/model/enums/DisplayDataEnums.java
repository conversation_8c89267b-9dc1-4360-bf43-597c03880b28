package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: mingyang.chen
 * @Date: 2021/1/20 17:30
 */
public enum DisplayDataEnums {
    Display_ZERO(0,"NO"),
    Display_ONE(1,"YES"),
    Display_TWO(2,"/")
    ;
    private final Integer code;
    private final String name;

    DisplayDataEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static final Map<Integer, DisplayDataEnums> maps = new HashMap<>();

    static {
        for (DisplayDataEnums enu : DisplayDataEnums.values()) {
            maps.put(enu.getCode(), enu);
        }
    }

    public static DisplayDataEnums getDisplay(Integer code) {
        return Optional.ofNullable(maps.get(code)).orElse(Display_TWO);
    }
}
