package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum EnquiryStatus {
    New(0, "New"),
    Closed(10, "Closed"),
    Completed(20,"Completed"),
    Cancelled(30,"Cancelled")
    ;

    private final int type;
    private final String message;

    EnquiryStatus(int type, String message) {
        this.type = type;
        this.message = message;
    }
    public int getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, EnquiryStatus> maps = new HashMap<Integer, EnquiryStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (EnquiryStatus enu : EnquiryStatus.values()) {
                put(enu.getType(), enu);
            }
        }
    };

    public static EnquiryStatus findType(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }

    /**
     *
     * @param status
     * @param type
     * @return
     */
    public static boolean check(Integer status, EnquiryStatus type) {
        if (status == null || !maps.containsKey(status.intValue())){
            return false;
        }
        return maps.get(status.intValue()) == type;
    }

    public boolean check(EnquiryStatus... types){
        if (types == null || types.length <= 0){
            return false;
        }
        for (EnquiryStatus type: types){
            if (this.getType() == type.getType()){
                return true;
            }
        }
        return false;
    }
}
