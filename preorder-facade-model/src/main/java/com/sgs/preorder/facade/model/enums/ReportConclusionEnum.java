package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: mingyang.chen
 * @Date: 2021/1/21 16:34
 */
public enum ReportConclusionEnum {
    Pass("Pass"),
    Fail("Fail"),
    None("None"),
    ;

    private final String name;

    ReportConclusionEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static final Map<String, ReportConclusionEnum> maps = new HashMap<>();

    static {
        for (ReportConclusionEnum value : values()) {
            maps.put(value.getName(), value);
        }
    }

    public static ReportConclusionEnum getInstance(String name) {
        return Optional.ofNullable(maps.get(name)).orElse(None);
    }
}
