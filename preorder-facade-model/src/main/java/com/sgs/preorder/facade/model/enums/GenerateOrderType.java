package com.sgs.preorder.facade.model.enums;

public enum GenerateOrderType {
    All(1,"all"),

    Split(2,"split");

    private final int code;
    private final String message;

    GenerateOrderType(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
