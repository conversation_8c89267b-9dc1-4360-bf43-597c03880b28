package com.sgs.preorder.facade.model.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sgs.framework.core.base.BaseProductLine;

/**
 * Created by <PERSON> on 2019/05/09.
 */
@Deprecated
public class CustomResult<T>  extends BaseProductLine {
    /**
     *
     */
    public CustomResult(){

    }
    /**
     *
     * @param isSuccess
     */
    public CustomResult(boolean isSuccess){
        this.success = isSuccess;
    }
    /**
     *
     */
    private boolean success;
    /**
     *
     */
    private T data;
    /**
     *
     */
    private int resultType;
    /**
     *
     */
    private String msg;
    /**
     *
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String stackTrace;
    /**
     *
     */
    private boolean ignore;

    private String errorCode;

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public int getResultType() {
        return resultType;
    }

    public void setResultType(int resultType) {
        this.resultType = resultType;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getStackTrace() {
        return stackTrace;
    }

    public void setStackTrace(String stackTrace) {
        this.stackTrace = stackTrace;
    }

    public boolean isIgnore() {
        return ignore;
    }

    public void setIgnore(boolean ignore) {
        this.ignore = ignore;
    }
}