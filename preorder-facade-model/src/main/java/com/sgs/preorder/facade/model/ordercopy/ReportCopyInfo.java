package com.sgs.preorder.facade.model.ordercopy;

import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.preorder.dbstorages.mybatis.model.ReportInfoPO;

public class ReportCopyInfo extends BaseProductLine {
    /**
     *
     */
    private String oldOrderNo;
    /**
     *
     */
    private String oldOrderID;
    /**
     *
     */
    private ReportInfoPO report;
    /**
     *
     */
    private ReportInfoPO oldReport;

    public String getOldOrderNo() {
        return oldOrderNo;
    }

    public void setOldOrderNo(String oldOrderNo) {
        this.oldOrderNo = oldOrderNo;
    }

    public ReportInfoPO getReport() {
        return report;
    }

    public void setReport(ReportInfoPO report) {
        this.report = report;
    }

    public ReportInfoPO getOldReport() {
        return oldReport;
    }

    public void setOldReport(ReportInfoPO oldReport) {
        this.oldReport = oldReport;
    }

    public String getOldOrderID() {
        return oldOrderID;
    }

    public void setOldOrderID(String oldOrderID) {
        this.oldOrderID = oldOrderID;
    }
}
