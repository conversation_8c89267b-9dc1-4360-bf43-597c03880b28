package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: OrderActionTypeEnum
 * @projectName preorder-service
 * @description: 订单状态变更Action，对应Common DB中的action字段
 * @date 2021/11/218:31
 */
public enum OrderActionTypeEnum {
    ALL_TL_COMPLATED("ALL TL Complated", "ALL TL Complated"),
    SAVE("save", "save"),
    ALL_TL_VALIDATED("AllTLValidated", "AllTLValidated"),
    QUOTATION_CONFIRMED("QuotationConfirmed", "QuotationConfirmed"),
    CANCELLED("Cancelled", "Cancelled"),
    FIRSTLABIN("FirstLabIn", "FirstLabIn"),
    UNPENDING("UnPending", "UnPending"),
    SOFTHARDCOPY_DELIVERY("SoftHardCopyDelivery", "SoftHardCopyDelivery"),
    PENDING("Pending", "Pending"),
    CONFIRM_MATRIX("ConfirmMatrix", "ConfirmMatrix"),
    REWORK_APPROVE("ReworkApprove", "ReworkApprove"),
    TRACKING_UPLOAD("TrackingUpload", "TrackingUpload");

    private String actionCode;
    private String desc;

    OrderActionTypeEnum(String actionCode, String desc) {
        this.actionCode = actionCode;
        this.desc = desc;
    }

    public String getActionCode() {
        return actionCode;
    }

    public String getCode() {
        return this.desc;
    }

    static Map<String, OrderActionTypeEnum> maps = new HashMap<>();

    static {
        for (OrderActionTypeEnum type : OrderActionTypeEnum.values()) {
            maps.put(type.getActionCode(), type);
        }
    }

    public static OrderActionTypeEnum findCode(Integer desc) {
        //jira-11232
        if (desc == null || !maps.containsKey(desc.toString())) {
            return null;
        }
        return maps.get(desc.toString());
    }

    public static boolean check(String actionCode) {
        if (actionCode == null) {
            return false;
        }
        return maps.containsKey(actionCode);
    }

    /**
     * @param status
     * @param amendType
     * @return
     */
    public static boolean check(Integer status, OrderActionTypeEnum amendType) {
        if (status == null || !maps.containsKey(status.toString())) {
            return false;
        }
        return maps.get(status.toString()) == amendType;
    }
}
