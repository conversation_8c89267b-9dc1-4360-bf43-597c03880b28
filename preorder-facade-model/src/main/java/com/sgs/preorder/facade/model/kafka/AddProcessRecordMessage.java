package com.sgs.preorder.facade.model.kafka;

import com.sgs.framework.core.base.BaseProductLine;

import java.util.List;

public class AddProcessRecordMessage extends BaseProductLine {
    /**
     * 状态为paperConsolidation的ordernos，供notes 将report的状态置为Combined
     */
    private List<String> paperConsolidationOrderNos;

    private List<CopyDeliveredMessage> copyDeliveredMessages;

    private List<String>  updateOrderStatusOrderNos;

    private List<UpdateJobMessage> updateJobMessages;

    private List<String> invoiceConfirms;

    public List<String> getPaperConsolidationOrderNos() {
        return paperConsolidationOrderNos;
    }

    public void setPaperConsolidationOrderNos(List<String> paperConsolidationOrderNos) {
        this.paperConsolidationOrderNos = paperConsolidationOrderNos;
    }

    public List<CopyDeliveredMessage> getCopyDeliveredMessages() {
        return copyDeliveredMessages;
    }

    public void setCopyDeliveredMessages(List<CopyDeliveredMessage> copyDeliveredMessages) {
        this.copyDeliveredMessages = copyDeliveredMessages;
    }

    public List<String> getUpdateOrderStatusOrderNos() {
        return updateOrderStatusOrderNos;
    }

    public void setUpdateOrderStatusOrderNos(List<String> updateOrderStatusOrderNos) {
        this.updateOrderStatusOrderNos = updateOrderStatusOrderNos;
    }

    public List<UpdateJobMessage> getUpdateJobMessages() {
        return updateJobMessages;
    }

    public void setUpdateJobMessages(List<UpdateJobMessage> updateJobMessages) {
        this.updateJobMessages = updateJobMessages;
    }

    public List<String> getInvoiceConfirms() {
        return invoiceConfirms;
    }

    public void setInvoiceConfirms(List<String> invoiceConfirms) {
        this.invoiceConfirms = invoiceConfirms;
    }
}
