package com.sgs.preorder.facade.model.ordercopy;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.List;

@Data
public class OrderCopyReq extends BaseRequest {
    /**
     *  参照 OrderCopyType
     */
    private int copyType;
    /**
     *
     */
    private String orderNo;
    /**
     *
     */
    private String oldOrderNo;
    /**
     *
     */
    private String subContractNo;
    /**
     *
     */
    private String postfix;
    private String[] productCategories;

    private String sourceBuCode;
    private String targetBuCode;
    private String enquiryId;
    private String enquiryNo;
    //批量CopyOrder
    private List<String> orderIds;
    private String toDoId;
}
