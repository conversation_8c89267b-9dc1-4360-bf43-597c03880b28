package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum FrontPageRequiredEnums {

	NeedFrontPageconfirmationEmail(1,"NeedFrontPageconfirmationEmail"),
	FrontPageEmailed(2,"FrontPageEmailed"),
	FrontPageEmailedAndNeedRevise(3,"FrontPageEmailedAndNeedRevise"),
	FrontPageConfirmed(4,"FrontPageConfirmed");


	private Integer frontPageRequiredCode;
    private String message;
	FrontPageRequiredEnums(Integer frontPageRequiredCode, String message){
		this.frontPageRequiredCode = frontPageRequiredCode;
		this.message = message;
	}

	public Integer getFrontPageRequiredCode() {
		return this.frontPageRequiredCode;
	}
	
	public String getMessage() {
		return message;
	}

	public static final Map<Integer, FrontPageRequiredEnums> maps = new HashMap<Integer, FrontPageRequiredEnums>() {
		private static final long serialVersionUID = -8986866330615001847L;
		{
			for (FrontPageRequiredEnums enu : FrontPageRequiredEnums.values()) {
				put(enu.getFrontPageRequiredCode(), enu);
			}
		}
	};

	public static String getMessage(Integer status) {
		if (status == null || !maps.containsKey(status.intValue())) {
			return null;
		}
		return maps.get(status).getMessage();
	}

}
