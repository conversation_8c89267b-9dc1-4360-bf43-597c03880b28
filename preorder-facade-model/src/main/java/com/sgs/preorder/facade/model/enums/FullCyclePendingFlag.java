package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum FullCyclePendingFlag {
    None(0, "普通【默认值】"),
    Order(1, "OrderPendingFlag"),
    Job(2, "JobPendingFlag"),
    Subcontract(4, "SubcontractPendingFlag"),
    Report(8, "ReportPendingFlag"),
    TestLine(16, "TestLinePendingFlag");

    private int code;
    private String message;
    public static final Map<Integer, FullCyclePendingFlag> maps = new HashMap<Integer, FullCyclePendingFlag>() {

        {
            FullCyclePendingFlag[] var1 = FullCyclePendingFlag.values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                FullCyclePendingFlag type = var1[var3];
                this.put(type.getCode(), type);
            }

        }
    };

    private FullCyclePendingFlag(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public static FullCyclePendingFlag findType(Integer type) {
        return type != null && maps.containsKey(type) ? (FullCyclePendingFlag)maps.get(type) : null;
    }

    public static boolean check(Integer code, FullCyclePendingFlag testLineType) {
        if (code != null && testLineType != null) {
            return (code & testLineType.getCode()) > 0;
        } else {
            return false;
        }
    }

    public static boolean isPretreatment(String pretreatment) {
        return pretreatment == null ? false : pretreatment.toLowerCase().contains("pretreatment");
    }

    public static Integer calculateType(Integer type, FullCyclePendingFlag pendingFlag) {
        return type != null && pendingFlag != null ? type ^ pendingFlag.getCode() : null;
    }
}
