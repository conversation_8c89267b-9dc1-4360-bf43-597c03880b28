package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

@Deprecated
//未来使用RefSystemIdEnum
public enum ReferenceIDEnum {

	portalCustomerNumber(1,"portalCustomerNumber"),
	SGSMart(2,"sgsMart"),
	OLB(3,"OLB"),
	SEMIR(4,"SEMIR"),
	ANTA(5,"ANTA"),
	InspectionTool(6,"InspectionTool"),
	SubContract(8, "SubContract"),
	ExternalSubContract(9,"ExternalSubContract"),
	OTS(10,"OTSSubContract"),
	TIC(12,"TIC"),
	;

	private Integer code;
	private String name;
	ReferenceIDEnum(Integer code,String name){
		this.code = code;
		this.name = name;
	}

	public Integer getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

	public static final Map<Integer, ReferenceIDEnum> maps = new HashMap<Integer, ReferenceIDEnum>() {
		private static final long serialVersionUID = -8986866330615001847L;
		{
			for (ReferenceIDEnum refId : ReferenceIDEnum.values()) {
				put(refId.getCode(), refId);
			}
		}
	};

	public static boolean check(Integer code, ReferenceIDEnum refId) {
		if (code == null || !maps.containsKey(code.intValue())){
			return false;
		}
		return maps.get(code.intValue()) == refId;
	}

	public static boolean check(Integer code,ReferenceIDEnum ... refIds){
		if(code==null || !maps.containsKey(code.intValue())){
			return false;
		}
		for (ReferenceIDEnum refId : refIds) {
			if(refId.getCode().compareTo(code)==0){
				return true;
			}
		}
		return false;
	}
}
