package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: DffColumnHumpEnums
 * @projectName preorder-service
 * @description: TODO
 * @date 2023/5/89:04
 */
public enum DffColumnUnderLineEnums {
    AgeGroup("AgeGroup","age_group"),
    BuyerAliase("BuyerAliase","buyer_aliase"),
    BuyerOrgannization1("BuyerOrgannization1","buyer_organnization_code1"),
    BuyerOrgannization2("BuyerOrgannization2","buyer_organnization_code2"),
    BuyerOrgannization3("BuyerOrgannization3","buyer_organnization_code3"),
    BuyerOrgannizationCode1("BuyerOrgannizationCode1","buyer_organnization1"),
    BuyerOrgannizationCode2("BuyerOrgannizationCode2","buyer_organnization2"),
    BuyerOrgannizationCode3("BuyerOrgannizationCode3","buyer_organnization3"),
    BuyerSourcingOffice("BuyerSourcingOffice","buyer_sourcing_office"),
    Collection("Collection","collection"),
    Construction("Construction","construction"),
    CountryOfDestination("CountryOfDestination","country_of_destination"),
    CountryOfOrigin("CountryOfOrigin","country_of_origin"),
    EndUse1("EndUse1","end_use1"),
    FabricReport("FabricReport","fabric_report"),
    FabricWidth("FabricWidth","fabric_width"),
    FactoryID("FactoryID","factory_id"),
    FactoryName("FactoryName","factory_name"),
    FiberComposition("FiberComposition","fiber_composition"),
    FiberWeight("FiberWeight","fiber_weight"),
    FirstFPUNo("FirstFPUNo","first_fpu_no"),
    FirstPassFPUNo("FirstPassFPUNo","first_pass_fpu_no"),
    FirstTimeApplicationFlag("FirstTimeApplicationFlag","first_time_application_flag"),
    FPUNo("FPUNo","fpu_no"),
    FPUReportNo("FPUReportNo","fpu_report_no"),
    GPUNo("GPUNo","gpu_no"),
    ItemNo("ItemNo","item_no"),
    LotNo("LotNo","lot_no"),
    NoOfSample("NoOfSample","no_of_sample"),
    OtherSampleInformation("OtherSampleInformation","other_sample_information"),
    PeformanceCode("PeformanceCode","peformance_code"),
    PONo("PONo","po_no"),
    PreviousReportNo("PreviousReportNo","previous_report_no"),
    ProductCategory1("ProductCategory1","product_category1"),
    ProductCategory2("ProductCategory2","product_category2"),
    ProductColor("ProductColor","product_color"),
    ProductDescription("ProductDescription","product_description"),
    ProductionStage("ProductionStage","production_stage"),
    ProductType("ProductType","product_type"),
    RefCode1("RefCode1","ref_code1"),
    RefCode10("RefCode10","ref_code10"),
    RefCode2("RefCode2","ref_code2"),
    RefCode3("RefCode3","ref_code3"),
    RefCode4("RefCode4","ref_code4"),
    RefCode5("RefCode5","ref_code5"),
    RefCode6("RefCode6","ref_code6"),
    RefCode7("RefCode7","ref_code7"),
    RefCode8("RefCode8","ref_code8"),
    RefCode9("RefCode9","ref_code9"),
    SampleID("SampleID","Sample_id"),
    SampleReceivedDate("SampleReceivedDate","sample_received_date"),
    Season("Season","season"),
    Size("Size","size"),
    SpecialCustomerAttribute1("SpecialCustomerAttribute1","special_customer_attribute1"),
    SpecialCustomerAttribute10("SpecialCustomerAttribute10","special_customer_attribute10"),
    SpecialCustomerAttribute11("SpecialCustomerAttribute11","special_customer_attribute11"),
    SpecialCustomerAttribute12("SpecialCustomerAttribute12","special_customer_attribute12"),
    SpecialCustomerAttribute13("SpecialCustomerAttribute13","special_customer_attribute13"),
    SpecialCustomerAttribute14("SpecialCustomerAttribute14","special_customer_attribute14"),
    SpecialCustomerAttribute15("SpecialCustomerAttribute15","special_customer_attribute15"),
    SpecialCustomerAttribute16("SpecialCustomerAttribute16","special_customer_attribute16"),
    SpecialCustomerAttribute17("SpecialCustomerAttribute17","special_customer_attribute17"),
    SpecialCustomerAttribute18("SpecialCustomerAttribute18","special_customer_attribute18"),
    SpecialCustomerAttribute2("SpecialCustomerAttribute2","special_customer_attribute2"),
    SpecialCustomerAttribute3("SpecialCustomerAttribute3","special_customer_attribute3"),
    SpecialCustomerAttribute4("SpecialCustomerAttribute4","special_customer_attribute4"),
    SpecialCustomerAttribute5("SpecialCustomerAttribute5","special_customer_attribute5"),
    SpecialCustomerAttribute6("SpecialCustomerAttribute6","special_customer_attribute6"),
    SpecialCustomerAttribute7("SpecialCustomerAttribute7","special_customer_attribute7"),
    SpecialCustomerAttribute8("SpecialCustomerAttribute8","special_customer_attribute8"),
    SpecialCustomerAttribute9("SpecialCustomerAttribute9","special_customer_attribute9"),
    SpecialFinishing("SpecialFinishing","special_finishing"),
    SpecialProductAttribute1("SpecialProductAttribute1","special_product_attribute1"),
    SpecialProductAttribute10("SpecialProductAttribute10","special_product_attribute10"),
    SpecialProductAttribute2("SpecialProductAttribute2","special_product_attribute2"),
    SpecialProductAttribute3("SpecialProductAttribute3","special_product_attribute3"),
    SpecialProductAttribute4("SpecialProductAttribute4","special_product_attribute4"),
    SpecialProductAttribute5("SpecialProductAttribute5","special_product_attribute5"),
    SpecialProductAttribute6("SpecialProductAttribute6","special_product_attribute6"),
    SpecialProductAttribute7("SpecialProductAttribute7","special_product_attribute7"),
    SpecialProductAttribute8("SpecialProductAttribute8","special_product_attribute8"),
    SpecialProductAttribute9("SpecialProductAttribute9","special_product_attribute9"),
    StyleNo("StyleNo","style_no"),
    Supplier("Supplier","supplier"),
    SupplierNo("SupplierNo","supplier_no"),
    ThreadCount("ThreadCount","thread_count"),
    TrimReportNo("TrimReportNo","trim_report_no"),
    VendorNo("VendorNo","vendor_no"),
    YarnCount("YarnCount","yarn_count");

    private String fieldCode;
    private String underLineColumn;

    DffColumnUnderLineEnums(String fieldCode, String underLineColumn){
        this.fieldCode = fieldCode;
        this.underLineColumn = underLineColumn;
    }
    public static final Map<String, DffColumnUnderLineEnums> maps = new HashMap<String, DffColumnUnderLineEnums>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (DffColumnUnderLineEnums enu : DffColumnUnderLineEnums.values()) {
                put(enu.getFieldCode().toUpperCase(), enu);
            }
        }
    };
    public static String getUnderLineByFiledCode(String fieldCode){
        if (fieldCode == null || !maps.containsKey(fieldCode.toUpperCase())) {
            return null;
        }
        return maps.get(fieldCode.toUpperCase()).getUnderLineColumn();
    }

    public static DffColumnUnderLineEnums getByFiledCode(String fieldCode){
        if (fieldCode == null || !maps.containsKey(fieldCode.toUpperCase())) {
            return null;
        }
        return maps.get(fieldCode.toUpperCase());
    }

    public String getFieldCode() {
        return fieldCode;
    }

    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
    }

    public String getUnderLineColumn() {
        return underLineColumn;
    }

    public void setUnderLineColumn(String underLineColumn) {
        this.underLineColumn = underLineColumn;
    }
}
