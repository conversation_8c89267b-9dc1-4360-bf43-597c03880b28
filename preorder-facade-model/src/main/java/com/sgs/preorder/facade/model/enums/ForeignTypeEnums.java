package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ForeignTypeEnums {
    REPORT("report","report");

    private final String code;
    private final String message;

    ForeignTypeEnums(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<String, ForeignTypeEnums> maps = new HashMap<String, ForeignTypeEnums>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ForeignTypeEnums type : ForeignTypeEnums.values()) {
                put(type.getCode().toLowerCase(), type);
            }
        }
    };

    public static ForeignTypeEnums getCode(String code) {
        if (code == null || !maps.containsKey(code.toLowerCase())) {
            return null;
        }
        return maps.get(code.toLowerCase());
    }

    public static boolean check(String code, ForeignTypeEnums objectType) {
        if (code == null || objectType == null || !maps.containsKey(code.toLowerCase())){
            return false;
        }
        return maps.get(code.toLowerCase()) == objectType;
    }
}
