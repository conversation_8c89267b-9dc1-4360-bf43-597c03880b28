package com.sgs.preorder.facade.model.enums;

public enum DeliveryTodoStatusEnums {
    NEW((byte)1, "New"),
    DELIVERED((byte)98,"Delivered"),
    CANCEL((byte)99, "Cancel");

    private byte code;
    private String name;
    private DeliveryTodoStatusEnums(byte code, String name){
        this.code = code;
        this.name= name;
    }

    public byte getCode(){
        return this.code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static DeliveryTodoStatusEnums enumOf(Byte code) {
        for(DeliveryTodoStatusEnums type: DeliveryTodoStatusEnums.values()) {
            if(type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
