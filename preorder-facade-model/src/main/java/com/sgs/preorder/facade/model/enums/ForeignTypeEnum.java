package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ForeignTypeEnum {
    quotationDiscount("quotationDiscount", "quotationDiscount"),
    reportTemplate("reportTemplate", "reportTemplate");

    private String code;
    private String name;

    ForeignTypeEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return this.code;
    }

    static Map<String, ForeignTypeEnum> maps = new HashMap<>();

    static {
        for (ForeignTypeEnum type : ForeignTypeEnum.values()) {
            maps.put(type.getCode(), type);
        }
    }

    public static ForeignTypeEnum findCode(String code) {
        if (code == null || !maps.containsKey(code)){
            return null;
        }
        return maps.get(code);
    }

    public static boolean check(String code) {
        if (code == null){
            return false;
        }
        return maps.containsKey(code);
    }

    /**
     *
     * @param code
     * @param amendType
     * @return
     */
    public static boolean check(String code, ForeignTypeEnum amendType) {
        if (code == null || !maps.containsKey(code)){
            return false;
        }
        return maps.get(code) == amendType;
    }
}