package com.sgs.preorder.facade.model.kafka;

import com.sgs.framework.core.base.BaseProductLine;

public class UpdateJobMessage extends BaseProductLine {
    private String jobNo;
    private Long date;
    private String type;
    private String remark;

    public String getJobNo() {
        return jobNo;
    }

    public void setJobNo(String jobNo) {
        this.jobNo = jobNo;
    }

    public Long getDate() {
        return date;
    }

    public void setDate(Long date) {
        this.date = date;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
