package com.sgs.preorder.facade.model.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BaseProductLineDTO extends BaseDTO{
    /**
     * 产品线编码
     */
    @ApiModelProperty(value = "产品线编码")
//    @NotEmpty(message = "{productline.notnull}",groups = {ValidationUpdateGroup.class, ValidationSaveGroup.class})
    private String productLineCode;
//    /**
//     * 产品线名称
//     */
//    private String productLineName;



}
