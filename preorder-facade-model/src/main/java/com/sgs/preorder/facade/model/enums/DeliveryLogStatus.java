package com.sgs.preorder.facade.model.enums;

public enum DeliveryLogStatus {
    New("10", "New"),
    Success("20", "Success"),
    Error("99", "Error");
    private String status;
    private String value;

    DeliveryLogStatus(String status, String value) {
        this.status = status;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public String getStatus() {
        return status;
    }
}
