package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum OrderStatusEnum {
	New(1, (1 << 0), "New") {
		@Override
		public int getRuleId() {
            int ruleId = OrderStatusEnum.computeRuleId(Confirmed,Pending,Cancelled,Testing,Completed);
            return ruleId;
		}
	},
	Quoted(2, (1 << 1), "Quoted") {
		@Override
		public int getRuleId() {
			return super.getRuleId();
		}
	},
	Confirmed(3, (1 << 2), "Confirmed") {
		@Override
		public int getRuleId() {
			int ruleId = OrderStatusEnum.computeRuleId(Pending,Cancelled,Testing,Reporting,Completed);
			return ruleId;
		}
	},
	Invoiced(4, (1 << 3), "Invoiced") {
		@Override
		public int getRuleId() {
			return super.getRuleId();
		}
	},
	Closed(5, (1 << 4), "Closed") {
		@Override
		public int getRuleId() {
			int ruleId = OrderStatusEnum.computeRuleId(Pending,Cancelled);
			return ruleId;
		}
	},
	Pending(6, (1 << 5), "Pending") {
		@Override
		public int getRuleId() {
		    //属于特殊流程
            int ruleId = OrderStatusEnum.computeRuleId(New,Confirmed,Closed,Testing,Reporting,Completed);
            return ruleId;
		}
	},
	Cancelled(7, (1 << 6), "Cancelled") {
		@Override
		public int getRuleId() {
			return super.getRuleId();
		}
	},
	Testing(8, (1 << 7), "Testing") {
		@Override
		public int getRuleId() {
			int ruleId = OrderStatusEnum.computeRuleId(Pending,Cancelled,Reporting,Completed);
			return ruleId;
		}
	},
	Reporting(9, (1 << 8), "Reporting") {
		@Override
		public int getRuleId() {
			int ruleId = OrderStatusEnum.computeRuleId(Pending,Cancelled,Completed);
			return ruleId;
		}
	},
	Completed(10, (1 << 9), "Completed") {
		@Override
		public int getRuleId() {
			int ruleId = OrderStatusEnum.computeRuleId(Closed,Pending,Cancelled);
			return ruleId;
		}
	};

	public static final Map<Integer, OrderStatusEnum> maps = new HashMap<Integer, OrderStatusEnum>() {
		private static final long serialVersionUID = -8986866330615001847L;

		{
			for (OrderStatusEnum enu : OrderStatusEnum.values()) {
				put(enu.getStatus(), enu);
			}
		}
	};
	private Integer status;
	private int code;
	private String message;

	OrderStatusEnum(Integer status, int code, String message) {
		this.status = status;
		this.code = code;
		this.message = message;
	}

	public static OrderStatusEnum getOrderStatus(Integer status) {
		if (status == null || !maps.containsKey(status.intValue())) {
			return null;
		}
		return maps.get(status.intValue());
	}

	public static String getMessage(Integer status) {
		if (status == null || !maps.containsKey(status.intValue())) {
			return null;
		}
		return maps.get(status).getMessage();
	}

    /**
     * 校验普通流程状态合法性
     * @param status
     * @param newStatus
     * @return
     */
	public static boolean checkNormalStatus(Integer status, int newStatus) {
		if (status == null || !maps.containsKey(status.intValue()) || !maps.containsKey(newStatus)) {
			return false;
		}
		return (maps.get(status).getRuleId() & maps.get(newStatus).getCode()) > 0;
	}

    /**
     * 校验回退流程合法性
     * @param status
     * @param newStatus
     * @return
     */
	public static boolean checkBackStatus(Integer status,int newStatus){
        if (status == null || !maps.containsKey(status.intValue()) || !maps.containsKey(newStatus)) {
            return false;
        }
        switch (status.intValue()){
            case 5:
                return newStatus== OrderStatusEnum.Completed.status;
            case 9:
                return newStatus== OrderStatusEnum.Testing.status;
            case 10:
                return newStatus== OrderStatusEnum.Reporting.status;
        }
        return false;
    }

    private static int computeRuleId(OrderStatusEnum...statuses) {
        if (statuses.length == 0) {
            return 0;
        }
        int result = statuses[0].getCode();
        for (int i = 1; i < statuses.length; i++) {
            result |= statuses[i].getCode();
        }
        return result;
    }

	public String getMessage() {
		return message;
	}

	public Integer getStatus() {
		return status;
	}

	public int getCode() {
		return code;
	}

	public int getRuleId() {
		return 0;
	}
}