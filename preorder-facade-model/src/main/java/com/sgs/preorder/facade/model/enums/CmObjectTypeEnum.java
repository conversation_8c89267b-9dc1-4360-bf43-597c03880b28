package com.sgs.preorder.facade.model.enums;


/**
 * Object Setting Object Type 对应枚举
 */
public enum CmObjectTypeEnum {
    Section(1,"section"),
    Field(2,"field"),
    Object(3,"object"),
    SubSection(4,"subSection");
    private Integer type;
    private String name;

    CmObjectTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }


    public Integer getType() {
        return type;
    }


    public String getName() {
        return name;
    }
}
