package com.sgs.preorder.facade.model.ordercopy;

import com.sgs.framework.core.base.BaseProductLine;

import java.util.Map;
import java.util.Set;

public class CopyReportInfo extends BaseProductLine {
    /**
     *
     */
    private String groupId;
    /**
     *
     */
    private Set<String> testMatrixIds;
    /**
     *
     */
    private Map<String, String> originalSampleIds;

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public Set<String> getTestMatrixIds() {
        return testMatrixIds;
    }

    public void setTestMatrixIds(Set<String> testMatrixIds) {
        this.testMatrixIds = testMatrixIds;
    }

    public void setOriginalSampleIds(Map<String, String> originalSampleIds) {
        this.originalSampleIds = originalSampleIds;
    }

    public Map<String, String> getOriginalSampleIds() {
        return originalSampleIds;
    }
}
