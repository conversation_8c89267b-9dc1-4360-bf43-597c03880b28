package com.sgs.preorder.facade.model.ordercopy;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.preorder.facade.model.info.ExeReportInfo;
import com.sgs.preorder.facade.model.info.user.UserLabBuInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CopyReportReq extends BaseRequest {
    /**
     *
     */
    private String orderNo;
    /**
     *
     */
    private String reportNo;
    /**
     *
     */
    private String reportId;
    /**
     * AmendReport
     * 1、Supplement -- 31
     * 2、Extract    -- 32
     * 3、Replace    -- 33
     *
     * SplitReport
     * 1、Sample     -- TestSampleId
     * 2、Conclusion -- TestSampleId+TestLineInstanceId
     * 3、TestLine   -- TestLineInstanceId
     */
    private int copyType;
    /**
     *
     */
    private List<TestMatrixInfo> testMatrixs;
    /**
     * AmendReport
     * 0:按Order 处理
     * 1:按Report处理
     */
    private int amendReportType;
    /**
     * 需要在Amend同时 创建的内部分包 Id
     */
    private List<String> subContractIds;

    /**
     * transToLang
     * 翻译的目标语言类型  1,2
     * @return
     */
    private int transToLang;

    // 邮件申请临时传递用户信息
    private UserLabBuInfo userLabBuInfo;
    private String sgsToken;
    private String regionAccount;
    private List<ExeReportInfo> exeReportList;

    private Date newReportExpectDueDate;

    // 标记是否是通过Draft申请创建的正式申请
    private Boolean draftApply;
}
