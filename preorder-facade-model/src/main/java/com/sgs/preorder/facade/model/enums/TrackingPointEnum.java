package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum TrackingPointEnum {

    OrderCreation(220, "orderCreation","Order Creation"),
    OrderPending(220, "orderPending","Order Pending"),
    SampleSeparation(114,"cuttingOut", "Sample Separation"),
    Consolidation(221, "paperConsolidation","Consolidation"),
    InvoiceConfirm(221, "invoiceConfirm","Invoice Confirm"),
    ParcelReceived(223,"parcelReceived","Parcel Received"),
    ConfirmMatrix(224,"confirmMatrix","Confirm Matrix"),
    PhotoTaking(225,"photoTaking","Photo Taking");


    private final int code;
    private final String pointCode;
    private final String message;

    TrackingPointEnum(int code, String pointCode, String message) {
        this.code = code;
        this.pointCode = pointCode;
        this.message = message;
    }

    public String getMessage() {
        return this.message;
    }

    public String getPointCode() {
        return this.pointCode;
    }

    public static final Map<Integer, TrackingPointEnum> CACHE = new HashMap<Integer, TrackingPointEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (TrackingPointEnum enu : TrackingPointEnum.values()) {
                put(enu.code, enu);
            }
        }
    };

    public static TrackingPointEnum getCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TrackingPointEnum item : TrackingPointEnum.values()) {
            if (code.equals(item.code)) {
                return item;
            }
        }
        return null;
    }

    public static String getMessage(Integer status) {
        if (status == null) {
            return null;
        }
        TrackingPointEnum trackingPoint = CACHE.get(status);
        return trackingPoint == null ? null : trackingPoint.getMessage();
    }
}
