package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum WfStatusEnums {
    New(0, "New"),
    Approved(1, "Approved"),
    Reject(2, "Reject")
    ;

    private Integer status;
    private String message;

    WfStatusEnums(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, WfStatusEnums> maps = new HashMap<Integer, WfStatusEnums>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (WfStatusEnums enu : WfStatusEnums.values()) {
                put(enu.status, enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }
}
