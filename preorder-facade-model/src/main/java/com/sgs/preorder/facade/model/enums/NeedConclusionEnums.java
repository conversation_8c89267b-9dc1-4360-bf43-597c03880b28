package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum NeedConclusionEnums {
	ReportConclusion(1, "NotToBossOrDraft"),
	NotNeedConclusion(0, "TobossButNotComplete"),
	NeedTLConclusion(2, "TobossButNotComplete"),
	NotNeedTLConclusion(3, "TobossButNotComplete"),
	PartTLConclusion(4, "Imported");
	private Integer status;
	private String value;

	NeedConclusionEnums(Integer status, String value) {
		this.status = status;
		this.value = value;
	}

	public String getValue() {
		return value;
	}

	public Integer getStatus() {
		return status;
	}

	public static final Map<Integer, NeedConclusionEnums> maps = new HashMap<Integer, NeedConclusionEnums>() {
		private static final long serialVersionUID = -8986866330615001847L;
		{
			for (NeedConclusionEnums enu : NeedConclusionEnums.values()) {
				put(enu.getStatus(), enu);
			}
		}
	};

	public static boolean check(Integer status, NeedConclusionEnums... bossStatus) {
		if (status == null || !maps.containsKey(status.intValue()) || bossStatus == null || bossStatus.length <= 0){
			return false;
		}
		for (NeedConclusionEnums tlStatus: bossStatus){
			if (status.intValue() == tlStatus.getStatus()){
				return true;
			}
		}
		return false;
	}
	
	public static String getValue(Integer status) {
		if (status == null || status == 0) {
			return null;
		}
		NeedConclusionEnums bossStatus = maps.get(status);
		return bossStatus == null ? null : bossStatus.getValue();
	}
}
