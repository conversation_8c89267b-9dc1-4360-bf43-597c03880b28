package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum StartDateType {
    SampleReceiveDate(1, "SampleReceiveDate"),
    TestLineDueDate(2, "TestLineDueDate");

    private Integer status;
    private String message;

    StartDateType(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, StartDateType> maps = new HashMap<Integer, StartDateType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (StartDateType enu : StartDateType.values()) {
                put(enu.status, enu);
            }
        }
    };

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }
}
