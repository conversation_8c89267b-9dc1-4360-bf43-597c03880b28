package com.sgs.preorder.facade.model.enums;

import com.sgs.preorder.facade.model.dto.order.OrderDetailDto;
import com.sgs.preorder.facade.model.info.OrderPersonInfo;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum OrderPersonType {
    NONE(0, "none"),
    SALES(1, "sales"),
    TS(2, "ts"),
    CSA(3, "csa"),
    SUB_REPORT_REVIEWER(4, "subReportReviewer")
    ;

    OrderPersonType(Integer status, String code) {
        this.status = status;
        this.code = code;
    }

    private Integer status;
    private String code;

    public String getCode() {
        return code;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<String, OrderPersonType> maps = new HashMap<String, OrderPersonType>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (OrderPersonType orderPersonType : OrderPersonType.values()) {
                put(orderPersonType.getCode(), orderPersonType);
            }
        }
    };

    public static OrderPersonType getCode(String code) {
        if (code == null || code.length() == 0 || !maps.containsKey(code)) {
            return OrderPersonType.NONE;
        }
        return maps.get(code);
    }

    public static boolean check(String code, OrderPersonType type) {
        if (StringUtils.isEmpty(code) || !maps.containsKey(code)){
            return false;
        }
        return maps.get(code) == type;
    }

    public static boolean checkPersonCode(String code, OrderPersonType...orderPersonTypes){
        if (code == null || !maps.containsKey(code) || orderPersonTypes == null || orderPersonTypes.length <= 0) {
            return false;
        }
        for (OrderPersonType type : orderPersonTypes){
            if (code.equalsIgnoreCase(type.getCode())){
                return true;
            }
        }
        return false;
    }
    public static void setOrderPerson(OrderDetailDto orderDetailDto, List<OrderPersonInfo> orderPersons) {
        if (orderPersons == null || orderPersons.isEmpty()) {
            return;
        }
        for (OrderPersonInfo orderPerson : orderPersons) {
            orderPerson.setOldVersionId(orderPerson.hashCode());
            OrderPersonType orderPersonType = OrderPersonType.getCode(orderPerson.getPersonType());
            switch (orderPersonType) {
                case SALES:
                    orderDetailDto.setSalesPerson(orderPerson);
                    break;
                case TS:
                    orderDetailDto.setTsPerson(orderPerson);
                    break;
                case CSA:
                    orderDetailDto.setCsaPerson(orderPerson);
                    break;
                case SUB_REPORT_REVIEWER:
                    orderDetailDto.setSubReportReviewerName(orderPerson);
                    break;
            }
        }
    }
}
