package com.sgs.preorder.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum OperationType {
    Extract(1, "Extract"),
    Replaced(2, "Replaced"),
    Supplement(3, "Supplement"),
    TranslationReport(7, "TranslationReport"),

    SplitReport(4, "SplitReport"),

    SubContract(5, "SubContract"),
    NewSubContract(6, "NewSubContract"),
    LightSubContract(8, "LightSubContract"),
    SubContractBind(9, "SubContractBind"),

    ChargeOnly(10, "Charge Only"),
    CancelEnquiry(20,"CancelEnquiry")
    ;

    private int status;
    private String code;

    OperationType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return this.code;
    }

    static Map<Integer, OperationType> maps = new HashMap<>();

    static {
        for (OperationType type : OperationType.values()) {
            maps.put(type.getStatus(), type);
        }
    }

    public static OperationType findCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        return maps.get(code);
    }

    public static boolean check(Integer status) {
        if (status == null){
            return false;
        }
        return maps.containsKey(status.intValue());
    }

    /**
     *
     * @param status
     * @param amendType
     * @return
     */
    public static boolean check(Integer status, OperationType amendType) {
        if (status == null || !maps.containsKey(status.intValue())){
            return false;
        }
        return maps.get(status.intValue()) == amendType;
    }

    /**
     *
     * @param operationType
     * @param operationType
     * @return
     */
    public static boolean check(Integer operationType, OperationType... operationTypes) {
        if (operationType == null || !maps.containsKey(operationType.intValue()) || operationTypes == null || operationTypes.length <= 0){
            return false;
        }
        for (OperationType ct: operationTypes){
            if (operationType.intValue() == ct.getStatus()){
                return true;
            }
        }
        return false;
    }
}
