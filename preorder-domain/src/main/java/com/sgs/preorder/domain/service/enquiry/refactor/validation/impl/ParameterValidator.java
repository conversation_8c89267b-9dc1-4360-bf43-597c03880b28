package com.sgs.preorder.domain.service.enquiry.refactor.validation.impl;

import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.domain.service.enquiry.refactor.model.ValidationContext;
import com.sgs.preorder.domain.service.enquiry.refactor.model.ValidationResult;
import com.sgs.preorder.domain.service.enquiry.refactor.validation.ValidationHandler;
import com.sgs.preorder.facade.model.common.ResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 参数验证器
 * 对应原代码：generateOrder方法开头的参数校验逻辑
 * 
 * 验证内容：
 * 1. enquiryId不能为空 - 对应原代码：if(Func.isEmpty(generalType) || Func.isEmpty(enquiryId))
 * 2. generalType不能为空 - 对应原代码：if(Func.isEmpty(generalType) || Func.isEmpty(enquiryId))
 * 3. 用户信息不能为空 - 对应原代码：UserInfo userInfo = SystemContextHolder.getUserInfo(); if(Func.isEmpty(userInfo))
 */
@Slf4j
@Component
public class ParameterValidator extends ValidationHandler {
    
    @Override
    protected ValidationResult doValidate(ValidationContext context) {
        log.debug("Executing parameter validation for enquiry: {}", 
                context.getRequest().getEnquiryId());
        
        // 验证enquiryId和generalType不能为空
        // 对应原代码：if(Func.isEmpty(generalType) || Func.isEmpty(enquiryId))
        String enquiryId = context.getRequest().getEnquiryId();
        Integer generalType = context.getRequest().getGeneralType();
        
        if (Func.isEmpty(generalType) || Func.isEmpty(enquiryId)) {
            log.warn("Parameter validation failed: enquiryId={}, generalType={}", 
                    enquiryId, generalType);
            return ValidationResult.failure(
                    ResponseCode.PARAM_MISS.getMessage(),
                    ResponseCode.PARAM_MISS.getCode()
            );
        }
        
        // 验证用户信息不能为空
        // 对应原代码：UserInfo userInfo = SystemContextHolder.getUserInfo(); if(Func.isEmpty(userInfo))
        if (Func.isEmpty(context.getCurrentUser())) {
            log.warn("Parameter validation failed: currentUser is null for enquiry: {}", enquiryId);
            return ValidationResult.failure(
                    ResponseCode.TokenExpire.getMessage(),
                    ResponseCode.TokenExpire.getCode()
            );
        }

         // 验证Lab是否切换
        // 对应原代码：if (!Func.equalsSafe(enquiryInfoPO.getLabCode(),
        // userInfo.getCurrentLabCode()))
        String enquiryLabCode = context.getEnquiryInfo().getLabCode();
        String currentLabCode = context.getCurrentUser().getCurrentLabCode();
        if (!Func.equalsSafe(enquiryLabCode, currentLabCode)) {
            log.warn("Lab validation failed: enquiry lab {} != current lab {} for enquiry: {}",
                    enquiryLabCode, currentLabCode, context.getRequest().getEnquiryId());
            return ValidationResult.failure(
                    String.format("当前用户Lab(%s)已切换为(%s).", enquiryLabCode, currentLabCode),
                    ResponseCode.FAILURE.getCode());
        }
        
        log.debug("Parameter validation passed for enquiry: {}", enquiryId);
        return ValidationResult.success();
    }
    
    @Override
    protected String getValidatorName() {
        return "ParameterValidator";
    }
}