package com.sgs.preorder.domain.service.enquiry.refactor.validation.impl;

import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.domain.service.enquiry.refactor.model.ValidationContext;
import com.sgs.preorder.domain.service.enquiry.refactor.model.ValidationResult;
import com.sgs.preorder.domain.service.enquiry.refactor.validation.ValidationHandler;
import com.sgs.preorder.facade.model.common.ResponseCode;
import com.sgs.preorder.facade.model.enums.EnquiryStatus;
import com.sgs.preorder.facade.model.enums.GenerateOrderType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 状态验证器
 * 对应原代码：generateOrder方法中的状态验证逻辑
 * 
 * 验证内容：
 * 1. Enquiry状态必须允许生成订单 - 对应原代码：List<Integer> allowStatus =
 * Arrays.asList(EnquiryStatus.New.getType(),EnquiryStatus.Completed.getType());
 * 2. Split模式下splitOrderFlag验证 -
 * 对应原代码：if(Func.equals(GenerateOrderType.Split.getCode(),generalType))
 * 3. Lab切换验证 - 对应原代码：if (!Func.equalsSafe(enquiryInfoPO.getLabCode(),
 * userInfo.getCurrentLabCode()))
 */
@Slf4j
@Component
public class StatusValidator extends ValidationHandler {

    @Override
    protected ValidationResult doValidate(ValidationContext context) {
        log.debug("Executing status validation for enquiry: {}",
                context.getRequest().getEnquiryId());

        // 验证Enquiry状态是否允许生成订单
        // 对应原代码：List<Integer> allowStatus =
        // Arrays.asList(EnquiryStatus.New.getType(),EnquiryStatus.Completed.getType());
        List<Integer> allowStatus = Arrays.asList(EnquiryStatus.New.getType(), EnquiryStatus.Completed.getType());
        Integer currentStatus = context.getEnquiryInfo().getEnquiryStatus();

        if (!allowStatus.contains(currentStatus)) {
            log.warn("Status validation failed: current status {} not allowed for enquiry: {}",
                    currentStatus, context.getRequest().getEnquiryId());
            return ValidationResult.failure(
                    "当前状态不允许执行 generate order 操作！！",
                    ResponseCode.PARAM_VALID_ERROR.getCode());
        }

        // 验证Split模式下的splitOrderFlag
        // 对应原代码：if(Func.equals(GenerateOrderType.Split.getCode(),generalType))
        Integer generalType = context.getRequest().getGeneralType();
        if (Func.equals(GenerateOrderType.Split.getCode(), generalType)) {
            Integer splitOrderFlag = context.getEnquiryInfo().getSplitOrderFlag();
            if (Func.isNotEmpty(splitOrderFlag) && Func.equalsSafe(splitOrderFlag, 0)) {
                log.warn("Split order validation failed: splitOrderFlag is 0 for enquiry: {}",
                        context.getRequest().getEnquiryId());
                return ValidationResult.failure(
                        "当前订单不允许执行 Split order 操作！！",
                        ResponseCode.PARAM_VALID_ERROR.getCode());
            }
        }


        log.debug("Status validation passed for enquiry: {}", context.getRequest().getEnquiryId());
        return ValidationResult.success();
    }

    @Override
    protected String getValidatorName() {
        return "StatusValidator";
    }
}