package com.sgs.preorder.domain.service.enquiry.refactor;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.preorder.domain.service.enquiry.refactor.async.AsyncProcessor;
import com.sgs.preorder.domain.service.enquiry.refactor.model.OrderGenerationContext;
import com.sgs.preorder.domain.service.enquiry.refactor.model.OrderGenerationResult;
import com.sgs.preorder.domain.service.enquiry.refactor.model.ValidationContext;
import com.sgs.preorder.domain.service.enquiry.refactor.model.ValidationResult;
import com.sgs.preorder.domain.service.enquiry.refactor.strategy.OrderGenerationStrategy;
import com.sgs.preorder.domain.service.enquiry.refactor.strategy.OrderGenerationStrategyFactory;
import com.sgs.preorder.domain.service.enquiry.refactor.validation.ValidationChain;
import com.sgs.preorder.facade.model.req.EnquiryGenerateOrderReq;
import com.sgs.preorder.facade.model.rsp.EnquiryGenerateOrderRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 订单生成编排器 - 使用模板方法模式协调整个订单生成流程
 * 
 * 对应原代码：EnquiryServiceImpl.generateOrder 方法的主要流程结构
 * 
 * 主要职责：
 * 1. 定义标准的订单生成流程模板
 * 2. 协调各个组件的执行顺序
 * 3. 管理事务边界和错误处理
 * 4. 统一的性能监控和日志记录
 * 
 * 流程步骤：
 * 1. 统一验证 - 使用责任链模式处理所有验证逻辑（参数、状态、Matrix等）
 * 2. 数据准备 - 对应原代码的数据查询和准备
 * 3. 执行生成 - 对应原代码的核心生成逻辑
 * 4. 后置处理 - 对应原代码的事务后处理
 * 
 * <AUTHOR>
 * @since 2.0
 */
@Slf4j
@Component
public class OrderGenerationOrchestrator {

    @Autowired
    private ValidationChain validationChain;

    @Autowired
    private OrderDataBuilder orderDataBuilder;

    @Autowired
    private OrderGenerationStrategyFactory strategyFactory;

    @Autowired
    private AsyncProcessor asyncProcessor;

    @Autowired
    private TransactionManager transactionManager;

    @Autowired
    private OrderGenerationErrorHandler errorHandler;


    /**
     * 订单生成主流程 - 模板方法
     * 
     * 对应原代码：EnquiryServiceImpl.generateOrder 方法的完整流程
     * 
     * @param request 订单生成请求
     * @return 订单生成响应
     */
    public BaseResponse<EnquiryGenerateOrderRsp> generateOrder(EnquiryGenerateOrderReq request) {
        log.info("开始执行订单生成流程，enquiryId: {}, generateType: {}",
                request.getEnquiryId(), request.getGeneralType());

        long startTime = System.currentTimeMillis();

        try {
            // 1. 统一验证阶段 - 使用责任链模式处理所有验证
            // 对应原代码：所有的验证逻辑（参数、状态、Matrix等）
            executeValidationChain(request);

            // 2. 数据准备阶段
            // 对应原代码：数据查询和准备部分
            OrderGenerationContext context = prepareOrderGenerationData(request);

            // 3. 事务执行阶段
            // 对应原代码：transactionTemplate.execute() 事务处理
            OrderGenerationResult result = executeOrderGenerationInTransaction(context);

            // 4. 后置处理阶段
            // 对应原代码：事务提交后的异步处理逻辑
            processPostGeneration(result);

            // 5. 构建响应结果
            BaseResponse<EnquiryGenerateOrderRsp> response = buildSuccessResponse(result);

            long duration = System.currentTimeMillis() - startTime;
            log.info("订单生成流程完成，耗时: {}ms, 生成订单数: {}",
                    duration, result.getOrderNoList().size());

            return response;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("订单生成流程失败，耗时: {}ms, enquiryId: {}",
                    duration, request.getEnquiryId(), e);

            // 使用错误处理器处理异常
            OrderGenerationContext errorContext = null;
            try {
                errorContext = prepareOrderGenerationData(request);
            } catch (Exception contextException) {
                log.warn("准备错误处理上下文失败", contextException);
            }

            OrderGenerationResult errorResult = errorHandler.handleError(e, errorContext);
            return buildErrorResponseFromResult(errorResult);
        }
    }

    /**
     * 执行统一验证链 - 使用责任链模式处理所有验证
     * 
     * 对应原代码：generateOrder方法开头的所有验证逻辑
     * - if(Func.isEmpty(generalType) || Func.isEmpty(enquiryId)) 参数验证
     * - allowStatus.contains(originalStatus) 状态验证
     * - enquiryOrderMatrixDTOS 验证和 QTY 校验逻辑 Matrix验证
     * 
     * 【重要修复】：确保在验证前预加载必要的数据
     * 对应原代码：EnquiryInfoPO enquiryInfoPO =
     * enquiryInfoMapper.selectByPrimaryKey(enquiryId);
     * 
     * @param request 请求参数
     */
    private void executeValidationChain(EnquiryGenerateOrderReq request) {
        log.debug("开始执行统一验证链，enquiryId: {}", request.getEnquiryId());

        // 【修复】：预加载验证所需的数据，确保ValidationContext包含完整信息
        // 对应原代码：EnquiryInfoPO enquiryInfoPO =
        // enquiryInfoMapper.selectByPrimaryKey(enquiryId);
        ValidationContext validationContext = validationChain.prepareValidationContext(request);

        // 使用责任链模式执行所有验证：参数验证 -> 状态验证 -> Matrix验证
        ValidationResult result = validationChain.validate(validationContext);

        // 如果验证失败，抛出异常中断流程
        if (!result.isValid()) {
            log.warn("验证链执行失败，enquiryId: {}, 错误: {}",
                    request.getEnquiryId(), result.getErrorMessage());
            throw new IllegalArgumentException(result.getErrorMessage());
        }

        log.debug("统一验证链执行完成，所有验证通过，enquiryId: {}", request.getEnquiryId());
    }

    /**
     * 准备订单生成数据
     * 
     * 对应原代码：
     * - EnquiryDetailInfo enquiry =
     * enquiryInfoExtMapper.getEnquiryDetailInfo(enquiryId);
     * - List<EnquiryOrderMatrixDTO> enquiryOrderMatrixDTOS =
     * enquiryOrderMatrixInfoExtMapper.selectListByEnquiryId()
     * - BuObjectTemplateAllDTO buObjects =
     * buSettingService.getTemplateSettingList()
     * - OrderDetailDto order = generateOrderByEnquiry()
     * 
     * @param request 请求参数
     * @return 订单生成上下文
     */
    private OrderGenerationContext prepareOrderGenerationData(EnquiryGenerateOrderReq request) {
        log.debug("开始准备订单生成数据");

        // 使用OrderDataBuilder构建订单生成上下文
        OrderGenerationContext context = orderDataBuilder.buildOrderGenerationContext(request);

        log.debug("订单生成数据准备完成，enquiry: {}, matrices: {}",
                context.getEnquiry().getEnquiryNo(),
                context.getMatrices() != null ? context.getMatrices().size() : 0);

        return context;
    }

    /**
     * 在事务中执行订单生成
     * 
     * 对应原代码：
     * transactionTemplate.execute(new TransactionCallback<Object>() {
     * 
     * @Override
     *           public Object doInTransaction(TransactionStatus status) {
     *           // 核心订单生成逻辑
     *           }
     *           });
     * 
     * @param context 订单生成上下文
     * @return 订单生成结果
     */
    private OrderGenerationResult executeOrderGenerationInTransaction(OrderGenerationContext context) {
        log.debug("开始事务执行订单生成");

        try {
            // 根据生成类型选择对应的策略
            OrderGenerationStrategy strategy = strategyFactory.createStrategy(
                    context.getRequest().getGeneralType());

            // 使用事务管理器执行订单生成策略
            OrderGenerationResult result = transactionManager.executeInTransaction(context, strategy);

            log.debug("事务执行订单生成完成，生成订单数: {}",
                    result.getOrderNoList().size());

            return result;

        } catch (Exception e) {
            log.error("事务执行订单生成失败", e);
            throw e; // 重新抛出，让上层的错误处理器处理
        }
    }

    /**
     * 后置处理 - 异步处理非关键任务
     * 
     * 对应原代码：
     * - quotationFacade.generateOrderForEnquiryAfter() 异步调用
     * - scheduleTaskClient.sendEventMessage() 异步调用
     * - updateTatAfterGenerate() 异步调用
     * 
     * @param result 订单生成结果
     */
    private void processPostGeneration(OrderGenerationResult result) {
        log.debug("开始后置处理");

        try {
            // 异步处理非关键任务，不影响主流程
            asyncProcessor.processAsync(result);

            log.debug("后置处理启动完成");

        } catch (Exception e) {
            // 异步处理失败不影响主流程，只记录日志
            log.warn("后置处理启动失败，但不影响主流程", e);
        }
    }

    /**
     * 构建成功响应
     * 
     * 对应原代码：
     * enquiryGenerateOrderRsp.setMessage("New OrderNo.["+firstOrderNo[0]+"]");
     * return BaseResponse.success(enquiryGenerateOrderRsp);
     * 
     * @param result 订单生成结果
     * @return 成功响应
     */
    private BaseResponse<EnquiryGenerateOrderRsp> buildSuccessResponse(OrderGenerationResult result) {
        EnquiryGenerateOrderRsp response = new EnquiryGenerateOrderRsp();
        response.setMessage(result.getMessage());
        response.setFirstOrderId(result.getFirstOrderId());
        response.setFirstOrderNo(result.getFirstOrderNo());

        return BaseResponse.success(response);
    }

    /**
     * 构建错误响应
     * 
     * 对应原代码：
     * enquiryGenerateOrderRsp.setMessage("错误信息");
     * return BaseResponse.fail(enquiryGenerateOrderRsp);
     * 
     * @param exception 异常信息
     * @return 错误响应
     */
    private BaseResponse<EnquiryGenerateOrderRsp> buildErrorResponse(Exception exception) {
        EnquiryGenerateOrderRsp response = new EnquiryGenerateOrderRsp();
        response.setMessage("订单生成失败: " + exception.getMessage());

        return BaseResponse.fail(500, response.getMessage());
    }

    /**
     * 从错误处理结果构建错误响应
     * 
     * @param errorResult 错误处理结果
     * @return 错误响应
     */
    private BaseResponse<EnquiryGenerateOrderRsp> buildErrorResponseFromResult(OrderGenerationResult errorResult) {
        EnquiryGenerateOrderRsp response = new EnquiryGenerateOrderRsp();
        response.setMessage(errorResult.getMessage());
        response.setSuccess(false);

        return BaseResponse.fail(500, errorResult.getMessage());
    }


}