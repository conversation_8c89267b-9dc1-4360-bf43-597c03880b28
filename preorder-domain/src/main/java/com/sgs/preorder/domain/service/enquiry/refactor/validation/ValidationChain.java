package com.sgs.preorder.domain.service.enquiry.refactor.validation;

import com.sgs.preorder.dbstorages.mybatis.extmapper.enquiry.EnquiryOrderMatrixInfoExtMapper;
import com.sgs.preorder.dbstorages.mybatis.mapper.EnquiryInfoMapper;
import com.sgs.preorder.domain.service.enquiry.refactor.model.ValidationContext;
import com.sgs.preorder.domain.service.enquiry.refactor.model.ValidationResult;
import com.sgs.preorder.domain.service.enquiry.refactor.validation.impl.MatrixValidator;
import com.sgs.preorder.domain.service.enquiry.refactor.validation.impl.ParameterValidator;
import com.sgs.preorder.domain.service.enquiry.refactor.validation.impl.StatusValidator;
import com.sgs.preorder.facade.model.req.EnquiryGenerateOrderReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 验证链 - 使用责任链模式组织验证逻辑
 * 对应原代码：generateOrder方法开头的所有验证逻辑
 * 
 * 将原代码中分散的验证逻辑组织成责任链模式：
 * 1. 参数验证 - 对应 if(Func.isEmpty(generalType) || Func.isEmpty(enquiryId))
 * 2. 状态验证 - 对应 if(!allowStatus.contains(originalStatus))
 * 3. Matrix验证 - 对应 Split模式下的Matrix数据检查
 * 
 * 使用方式：
 * 
 * 【方式一：Spring自动构建（推荐）】
 * 通过构造函数注入自动构建验证链，无需手动调用buildChain方法：
 * ```java
 * @Autowired
 * private ValidationChain validationChain; // 自动构建完成
 * ValidationResult result = validationChain.validate(context);
 * ```
 * 
 * 【方式二：手动构建（测试/特殊场景）】
 * 使用buildChain方法手动构建验证链：
 * ```java
 * ValidationChain chain = new ValidationChain(param, status, matrix);
 * chain.buildChain(paramValidator, statusValidator, matrixValidator);
 * ValidationResult result = chain.validate(context);
 * ```
 * 
 * 【方式三：直接使用ValidationHandler（灵活性最高）】
 * 直接使用ValidationHandler的setNext方法：
 * ```java
 * ValidationHandler chain = paramValidator
 *     .setNext(statusValidator)
 *     .setNext(matrixValidator);
 * ValidationResult result = chain.handle(context);
 * ```
 */
@Slf4j
@Component
public class ValidationChain {
    
    private ValidationHandler firstHandler;
    @Autowired
    private EnquiryInfoMapper enquiryInfoMapper;

    @Autowired
    private EnquiryOrderMatrixInfoExtMapper enquiryOrderMatrixInfoExtMapper;

    /**
     * 通过Spring依赖注入自动构建验证链
     * 对应原代码中验证逻辑的执行顺序：参数验证 -> 状态验证 -> Matrix验证
     */
    @Autowired
    public ValidationChain(
            ParameterValidator parameterValidator,
            StatusValidator statusValidator,
            MatrixValidator matrixValidator) {
        
        // 按照业务逻辑顺序构建责任链
        this.firstHandler = parameterValidator;
        parameterValidator.setNext(statusValidator);
        statusValidator.setNext(matrixValidator);
        
        log.info("Validation chain auto-built with Spring injection: ParameterValidator -> StatusValidator -> MatrixValidator");
    }
    
    /**
     * 手动构建验证链（保留用于测试或特殊场景）
     * 对应原代码中验证逻辑的执行顺序
     */
    public void buildChain(ValidationHandler... handlers) {
        if (handlers == null || handlers.length == 0) {
            log.warn("Cannot build chain with empty handlers");
            return;
        }
        
        this.firstHandler = handlers[0];
        
        // 将所有处理器串联起来
        for (int i = 0; i < handlers.length - 1; i++) {
            handlers[i].setNext(handlers[i + 1]);
        }
        
        log.info("Validation chain manually built with {} handlers", handlers.length);
    }
    
    /**
     * 执行验证链
     * 对应原代码：generateOrder方法开头的所有验证逻辑
     * 
     * @param context 验证上下文
     * @return 验证结果
     */
    public ValidationResult validate(ValidationContext context) {
        if (firstHandler == null) {
            log.warn("Validation chain is empty, skipping validation");
            return ValidationResult.success();
        }
        
        log.debug("Starting validation chain for enquiry: {}", 
                context.getRequest().getEnquiryId());
        
        ValidationResult result = firstHandler.handle(context);
        
        if (result.isValid()) {
            log.debug("All validations passed for enquiry: {}", 
                    context.getRequest().getEnquiryId());
        } else {
            log.warn("Validation failed for enquiry: {}, error: {}", 
                    context.getRequest().getEnquiryId(), result.getErrorMessage());
        }
        
        return result;
    }
    /**
     * 准备验证上下文 - 预加载验证所需的数据
     *
     * 【重要修复】：确保ValidationContext包含验证器所需的所有数据
     * 对应原代码：
     * - EnquiryInfoPO enquiryInfoPO =
     * enquiryInfoMapper.selectByPrimaryKey(enquiryId);
     * - List<EnquiryOrderMatrixDTO> enquiryOrderMatrixDTOS =
     * enquiryOrderMatrixInfoExtMapper.selectListByEnquiryId()
     * - UserInfo userInfo = SystemContextHolder.getUserInfo();
     *
     * @param request 请求参数
     * @return 包含完整数据的验证上下文
     */
    public ValidationContext prepareValidationContext(EnquiryGenerateOrderReq request) {
        log.debug("开始准备验证上下文，enquiryId: {}", request.getEnquiryId());

        String enquiryId = request.getEnquiryId();

        // 1. 加载EnquiryInfo基础信息
        // 对应原代码：EnquiryInfoPO enquiryInfoPO =
        // enquiryInfoMapper.selectByPrimaryKey(enquiryId);
        com.sgs.preorder.dbstorages.mybatis.model.EnquiryInfoPO enquiryInfo = null;
        if (com.sgs.framework.tool.utils.Func.isNotEmpty(enquiryId)) {
            enquiryInfo = enquiryInfoMapper.selectByPrimaryKey(enquiryId);
            if (enquiryInfo == null) {
                log.warn("EnquiryInfo不存在，enquiryId: {}", enquiryId);
                throw new IllegalArgumentException("Enquiry不存在，enquiryId: " + enquiryId);
            }
        }

        // 2. 加载Matrix数据（Split模式验证需要）
        // 对应原代码：List<EnquiryOrderMatrixDTO> enquiryOrderMatrixDTOS =
        // enquiryOrderMatrixInfoExtMapper.selectListByEnquiryId(enquiryId,EnquiryOrderMatrixStatus.New.getCode());
        java.util.List<com.sgs.preorder.facade.model.dto.enquiry.EnquiryOrderMatrixDTO> matrices = null;
        if (com.sgs.framework.tool.utils.Func.isNotEmpty(enquiryId)) {
            try {
                matrices = enquiryOrderMatrixInfoExtMapper.selectListByEnquiryId(
                        enquiryId,
                        com.sgs.preorder.facade.model.enums.EnquiryOrderMatrixStatus.New.getCode());
            } catch (Exception e) {
                log.warn("加载Matrix数据失败，enquiryId: {}, 错误: {}", enquiryId, e.getMessage());
                // Matrix数据加载失败不影响基础验证，设置为空列表
                matrices = new java.util.ArrayList<>();
            }
        }

        // 3. 获取当前用户信息
        // 对应原代码：UserInfo userInfo = SystemContextHolder.getUserInfo();
        com.sgs.core.domain.UserInfo currentUser = com.sgs.framework.core.context.SystemContextHolder.getUserInfo();

        // 4. 获取批量标志
        // 对应原代码：boolean batchFlag = generateOrderReq.isBatchFlag();
        boolean batchFlag = request.isBatchFlag();

        // 构建ValidationContext
        ValidationContext validationContext = ValidationContext.builder()
                .request(request)
                .enquiryInfo(enquiryInfo)
                .matrices(matrices)
                .currentUser(currentUser)
                .batchFlag(batchFlag)
                .build();

        log.debug("验证上下文准备完成，enquiryInfo: {}, matrices: {}, user: {}",
                enquiryInfo != null ? enquiryInfo.getEnquiryNo() : "null",
                matrices != null ? matrices.size() : 0,
                currentUser != null ? currentUser.getName() : "null");

        return validationContext;
    }

}